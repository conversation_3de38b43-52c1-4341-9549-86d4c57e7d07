name: Kuberns Web CI/CD Pipeline

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      # - name: Renew SSL certificate # NOTE: Currently disabling this step until the renewal command rate limit from let's encrypt is lifted
      #   uses: appleboy/ssh-action@master
      #   continue-on-error: true  # <-- This allows the workflow to proceed even if this step fails
      #   with:
      #     host: ${{ secrets.KUBERNS_WEB_SERVER_PUBLIC_IP }}
      #     username: ${{ secrets.KUBERNS_WEB_SERVER_USER }}
      #     key: ${{ secrets.KUBERNS_WEB_SERVER_SSH_PRIVATE_KEY }}
      #     port: 22
      #     script: |
      #       echo "Running dry-run SSL renewal test..."
      #       if sudo certbot renew --dry-run --webroot --webroot-path /var/www/certbot/ --rsa-key-size 4096 --no-eff-email --non-interactive; then
      #         echo "Dry-run successful. Proceeding with actual SSL renewal..."
      #         sudo certbot renew --webroot --webroot-path /var/www/certbot/ --rsa-key-size 4096 --no-eff-email --non-interactive
      #       else
      #         echo "Dry-run failed. Skipping actual SSL renewal."
      #         exit 1
      #       fi
      #     # Command To Issue New One From Scratch
      #     # script: |
      #     #   sudo certbot certonly --webroot --webroot-path /var/www/certbot/ -d kuberns.com -d www.kuberns.com --agree-tos --no-eff-email --non-interactive --rsa-key-size 4096
      
      - name: Pull latest code
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.KUBERNS_WEB_SERVER_PUBLIC_IP }}
          username: ${{ secrets.KUBERNS_WEB_SERVER_USER }}
          key: ${{ secrets.KUBERNS_WEB_SERVER_SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd /var/www/kuberns-web
            sudo git reset --hard  # Ensures a clean state
            sudo git pull origin main  # Pulls the latest changes from the main branch

      - name: Build application
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.KUBERNS_WEB_SERVER_PUBLIC_IP }}
          username: ${{ secrets.KUBERNS_WEB_SERVER_USER }}
          key: ${{ secrets.KUBERNS_WEB_SERVER_SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd /var/www/kuberns-web
            sudo npm install  # Ensures all dependencies are up to date
            sudo npm run build

      - name: Reload Nginx
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.KUBERNS_WEB_SERVER_PUBLIC_IP }}
          username: ${{ secrets.KUBERNS_WEB_SERVER_USER }}
          key: ${{ secrets.KUBERNS_WEB_SERVER_SSH_PRIVATE_KEY }}
          port: 22
          script: sudo nginx -s reload

      - name: Restart application with PM2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.KUBERNS_WEB_SERVER_PUBLIC_IP }}
          username: ${{ secrets.KUBERNS_WEB_SERVER_USER }}
          key: ${{ secrets.KUBERNS_WEB_SERVER_SSH_PRIVATE_KEY }}
          port: 22
          script: sudo pm2 restart kuberns-web
