import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/**/*.{js,ts,jsx,tsx,mdx}",

  ],
  important: true,
  theme: {
    extend: {
      // custom colors for the project
      colors: {
        // background colors
        bg: {
          primary: "#111111",
          secondary: "#161616",
          tertiary: "#1F1F1F",
        },
        // text colors
        tx: {
          primary: "#CACACA",
          secondary: "#707070",
          tertiary: "#A0A0A0",
          "primary-1": "#D5D5D5",
          white: "#FFFFFF",
          "secondary-2": "#8B8B8B",
        },
        // brand colors
        br: {
          primary: "#2758D1",
          secondary: "#2766FE",
          accent: "#161723",
        },
        // stroke/border colors
        st: {
          light: "#212121",
          dark: "#111111",
        },
      },

      // custom fonts for the project
      fontFamily: {
        manrope: ["var(--font-manrope)", "sans-serif"],
        archivo: ["var(--font-archivo)", "sans-serif"],
        dmserif: ["var(--font-dmserif)", "serif"],
        dmsans: ["var(--font-dmsans)", "sans-serif"],
      },

      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
    },
  },
  plugins: [],
};
export default config;
