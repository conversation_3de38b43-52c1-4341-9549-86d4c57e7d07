/** @type {import('next').NextConfig} */
const nextConfig = {
  async redirects() {
    return [
      {
        source: "/signup",
        destination: "https://dashboard.kuberns.com/login",
        permanent: true,
      },
      {
        source: "/home",
        destination: "/",
        permanent: true,
      },
    ];
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "https://dh0pnjwiz7wby.cloudfront.net",
        port: "",
        pathname: "/**",
      },
    ],
  },
  // output: 'export',
  // assetPrefix: process.env.NODE_ENV === 'production' ? 'https://bunny.harshkanani.site' : '',
  images: {
    unoptimized: true,
  },
};

export default nextConfig;
