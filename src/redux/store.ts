import { configureStore } from "@reduxjs/toolkit";
import { TypedUseSelectorHook, useDispatch, useSelector } from "react-redux";
import appSlice from "./slices/appSlice";
import pricingSlice from "./slices/pricingSlice";
import toastReducer from "./slices/toastSlice";
import rootReducer from "./slices/rootSlice";
import templateReducer from "./slices/templateSlice";
const store = configureStore({
  reducer: {
    appSlice,
    pricingSlice,
    toast: toastReducer,
    root: rootReducer,
    template: templateReducer,
  },
});
// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
