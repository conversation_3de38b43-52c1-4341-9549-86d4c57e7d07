import apiClient from "@/utils/interceptor";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// define initial state here
const initialState = {
  isMarketingPopupActive: false,
  saveLoading: false,
};

export const saveUserEmail = createAsyncThunk(
  "root/saveUserEmail",
  async (payload: { email: string }) => {
    const res = await apiClient.post(
      "/monitoring/landing-page/update-user-email/",
      payload
    );

    return res.data.data;
  }
);

const rootSlice = createSlice({
  name: "root",
  initialState,
  reducers: {
    setIsMarketingPopupActive(state, action) {
      if (sessionStorage.getItem("hideMarketingPopup") === "true") return;

      state.isMarketingPopupActive = action.payload;

      if (action.payload === false) {
        sessionStorage.setItem("hideMarketingPopup", "true");
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(saveUserEmail.pending, (state, action) => {
      state.saveLoading = true;
    });

    builder.addCase(saveUserEmail.fulfilled, (state, action) => {
      state.saveLoading = false;
    });

    builder.addCase(saveUserEmail.rejected, (state, action) => {
      state.saveLoading = false;
    });
  },
});

export const { setIsMarketingPopupActive } = rootSlice.actions;

export default rootSlice.reducer;
