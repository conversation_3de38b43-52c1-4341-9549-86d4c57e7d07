import apiClient from "@/utils/interceptor";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

// define initial state here
const initialState = {
  isAnimationActive: false,
  isHomePage: false,
  toastMsgs: [
    "1,200 free trials claimed this month, 500 remaining! 🏃🏼",
  ] as string[],
};

export const getToastMsgs = createAsyncThunk("app/getToastMsgs", async () => {
  const res = await apiClient.get("monitoring/landing-page/get-short-descs/");
  return res.data.data;
});

const appSlice = createSlice({
  name: "app",
  initialState,
  reducers: {
    setIsAnimationActive: (state, action) => {
      state.isAnimationActive = action.payload;
    },
    setIsHomePage: (state, action) => {
      state.isHomePage = action.payload;
    },
  },

  extraReducers: (builder) => {
    builder.addCase(getToastMsgs.fulfilled, (state, action) => {
      state.toastMsgs = action.payload;
    });
  },
});

export const { setIsAnimationActive, setIsHomePage } = appSlice.actions;

export default appSlice.reducer;
