import apiClient from "@/utils/interceptor";
import InterceptorStaging from "@/utils/staging-interceptor";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
const initialState = {
  templateLoading: false,
  templates: [] as Template[],
  templateContent: {} as TemplateContent,
};

export interface Team {
  type: string;
  memory: string;
  members: string;
  monthly: number;
  yearly: number;
  comparison: Comparison[];
  server_config: string;
  storage_config: string;
}

export interface Comparison {
  name: string;
  yearly: number;
  monthly: number;
  logo_url: string;
}

export interface PricingCalculator {
  teams: Team[];
  teamDetails: TeamDetails[];
}

export interface TeamDetails {
  title: string;
  description: string;
}

export interface TypeOfFeature {
  name: string;
  description: string;
}

// Base template (can be reused)
export interface BaseTemplate {
  public_id: string;
  main_header: string;
  logo_url: string;
  short_description: string;
  alternatives: string[];
  pricing_calculator: PricingCalculator;
}

// Slim version
export interface Template extends BaseTemplate {}

// Full content version
export interface TemplateContent extends BaseTemplate {
  sub_header: string;
  sub_header_description: string;
  notes: string[];
  features_description: string;
  type_of_features: TypeOfFeature[];
  hero_section_logo_url: string;
  youtube_url: string;
  open_source_template_url: string;
  notes_header: string;
  notes_description: string;
  features_header: string;
  page_title:string
  meta_description:string
  h1:string[]
  h2:string[]
}

export const getTemplates = createAsyncThunk(
  "template/getTemplates",
  async () => {
    const res = await apiClient.get("open-source-templates/preview");
    return res.data.data;
  }
);

export const getTemplateContent = createAsyncThunk(
  "template/getTemplateContent",
  async (templateId: string) => {
    const res = await apiClient.get(`open-source-templates/list/${templateId}`);
    return res.data.data;
  }
);

const templateSlice = createSlice({
  name: "template",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getTemplates.pending, (state) => {
      state.templateLoading = true;
    });
    builder.addCase(getTemplates.fulfilled, (state, action) => {
      state.templates = action.payload;
      state.templateLoading = false;
    });
    builder.addCase(getTemplates.rejected, (state) => {
      state.templateLoading = false;
    });
    builder.addCase(getTemplateContent.pending, (state) => {
      state.templateLoading = true;
    });
    builder.addCase(getTemplateContent.fulfilled, (state, action) => {
      state.templateContent = action.payload;
      state.templateLoading = false;
    });
    builder.addCase(getTemplateContent.rejected, (state) => {
      state.templateLoading = false;
    });
  },
});

export default templateSlice.reducer;
