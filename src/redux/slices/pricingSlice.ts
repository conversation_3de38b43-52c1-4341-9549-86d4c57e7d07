import { sort<PERSON><PERSON><PERSON><PERSON> } from "@/utils";
import apiClient from "@/utils/interceptor";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

export interface Region {
  public_id: string;
  kuberns_region_name: string;
}

export interface PlanObj {
  public_id: string;
  name: string;
  resource_type: ResourceType;
  compute_cost_per_hour: number;
  default_vcpu: number;
  default_ram: number;
  default_data_transfer_limit: number;
  elastic_ram: string;
  elastic_vcpu: string;
}

type ResourceType = "SERVER" | "DATABASE" | "REDIS";

// define initial state here
const initialState = {
  regions: [] as Region[],
  usdToInrConversion: 82,
  plans: {
    SERVER: [] as PlanObj[],
    DATABASE: [] as PlanObj[],
    REDIS: [] as PlanObj[],
  },
};

export const getRegions = createAsyncThunk("pricing/getRegions", async () => {
  const res = await apiClient.get("backend-service/create-app/get-regions");
  return res.data.data;
});

export const getPlans = createAsyncThunk(
  "pricing/getServerPlans",
  async ({
    region,
    type,
    subType
  }: {
    region: string;
    type: "SERVER" | "DATABASE" | "REDIS";
    subType:string
  }) => {
    let dbTypeString = type === "DATABASE" ? "&db_type=MYSQL" : "";

    const res = await apiClient.get(
      `backend-service/create-app/get-pricing?region=${region}&plan_type=BASIC&resource_type=${type}${dbTypeString}&sub_resource_type=${subType}`
    );
    
    return { type, data: res.data.data };
  }
);

const pricingSlice = createSlice({
  name: "pricing",
  initialState,
  reducers: {},
  extraReducers(builder) {
    builder.addCase(getRegions.fulfilled, (state, action) => {
      state.regions = sortByKey(action.payload, "kuberns_region_name");
    });
    builder.addCase(getPlans.fulfilled, (state, action) => {
      state.plans = {
        ...state.plans,
        [action.payload.type]: action?.payload?.data?.data,
      };
      state.usdToInrConversion = action?.payload?.data?.inr_usd_conversion_rate
    });
  },
});

export default pricingSlice.reducer;
