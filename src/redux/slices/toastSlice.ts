// redux/toastSlice.js
import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  isOpen: false,
  showToast: true,
};

const toastSlice = createSlice({
  name: "toast",
  initialState,
  reducers: {
    openToast: (state) => {
      state.isOpen = true;
    },
    closeToast: (state) => {
      state.isOpen = false;
    },
    setHideToast: (state) => {
      state.showToast = false;
    },
  },
});

export const { openToast, closeToast, setHideToast } = toastSlice.actions;
export default toastSlice.reducer;
