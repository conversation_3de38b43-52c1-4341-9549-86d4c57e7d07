import { z } from "zod";

// Define the schema for environment variables
const configSchema = z.object({
  // Backend Configs
  NEXT_ENV: z.enum(["prod", "dev"]),

//   // Revalidate secret for ISR
//   REVALIDATE_SECRET: z.string(),

//   // Cloudfront URL
//   NEXT_PUBLIC_CLOUDFRONT_URL: z.string().url().optional(),

  // Strapi GraphQL URL
  STRAPI_GRAPHQL_URL: z.string().url(),

  // Strapi API Token
  STRAPI_API_TOKEN: z.string(),

  // Revalidate
  NEXT_PUBLIC_REVALIDATE: z.string(),

  // Base URL
  NEXT_PUBLIC_BASE_URL: z.string().url(),
});

// Parse and validate environment variables
export const config = configSchema.parse(process.env);
