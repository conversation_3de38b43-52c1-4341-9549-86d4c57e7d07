export const isBrowser = () => typeof window !== "undefined";

export interface IconProps {
  height?: string;
  width?: string;
  fill?: string;
}

export const formatStorageSize = (megabytes: number): string => {
  if (megabytes < 1024) {
    return `${megabytes} MB`;
  } else {
    const gigabytes = Math.round((megabytes / 1024) * 10) / 10;
    return `${gigabytes} GB`;
  }
};

export function sortByKey(array: any[], key: string, order = "asc") {
  return array.sort((a, b) => {
    let x = a[key];
    let y = b[key];

    // Handling string comparison in a case-insensitive manner
    if (typeof x === "string") {
      x = x.toLowerCase();
    }
    if (typeof y === "string") {
      y = y.toLowerCase();
    }

    // Determine the order of sorting
    if (order === "asc") {
      return x > y ? 1 : x < y ? -1 : 0;
    } else {
      return x < y ? 1 : x > y ? -1 : 0;
    }
  });
}
