import PageLayout from "@/components/Layout";
import { List, ListItem, Typography } from "@mui/material";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
    title: {
        absolute: "Refund and Cancellation Policy | Kuberns",
    },
    description:
        "<PERSON><PERSON><PERSON> believes in a liberal cancellation policy. Learn more about our refund, cancellation, and replacement guidelines.",
    alternates: {
        canonical: "https://kuberns.com/refund-cancellation-policy",
    },
    robots: {
        index: false,
        follow: false,
    },
};

function RefundCancellationPolicy() {
    const headingText = "#FFF";
    const subTitleText = "#CCCCCC";

    return (
        <main className="flex flex-col items-center font-manrope pt-16 md:pt-0 h-full pb-20">
            <PageLayout className="w-11/12 flex justify-center items-center flex-col pt-10 md:pt-20 gap-2">
                <Typography className="text-tx-primary font-manrope font-semibold text-2xl md:text-3xl">
                    Refund and Cancellation Policy
                </Typography>
                <Typography pt={2}>
                    At Kuberns, we believe in helping our customers as much as possible.
                    Our cancellation and refund policy is designed to be simple and clear.
                </Typography>
                <List component="ol">
                    <ListItem
                        sx={{ color: headingText }}
                        className="p-0 pt-3 text-[15px] font-manrope font-semibold flex flex-col items-start"
                    >
                        <Typography component="span">
                            1. Order Cancellations:
                        </Typography>
                        <ul className="pt-1 font-semibold pl-4" style={{ color: subTitleText }}>
                            <li className="font-[13px] p-0 pl-5">
                                Cancellations are allowed only within 3-5 days of placing the order.
                            </li>
                            <li className="font-[13px] p-0 pl-5">
                                If the order has already been communicated to vendors or shipped, cancellation may not be possible.
                            </li>
                        </ul>
                    </ListItem>

                    <ListItem
                        sx={{ color: headingText }}
                        className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
                    >
                        <Typography component="span">
                            2. Non-Cancellable Items:
                        </Typography>
                        <Typography className="pt-1 font-[13px] pl-4" style={{ color: subTitleText }}>
                            Perishable items such as flowers, food, and similar goods cannot be canceled. Refunds or replacements may be provided if product quality is proven to be unsatisfactory.
                        </Typography>
                    </ListItem>

                    <ListItem
                        sx={{ color: headingText }}
                        className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
                    >
                        <Typography component="span">
                            3. Damaged or Defective Products:
                        </Typography>
                        <Typography className="pt-1 font-[13px] pl-4" style={{ color: subTitleText }}>
                            If you receive a damaged or defective product, please report it to our Customer Service within 3-5 days of delivery. Our team will inspect and validate the issue before approving any replacement or refund.
                        </Typography>
                    </ListItem>

                    <ListItem
                        sx={{ color: headingText }}
                        className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
                    >
                        <Typography component="span">
                            4. Product Expectations:
                        </Typography>
                        <Typography className="pt-1 font-[13px] pl-4" style={{ color: subTitleText }}>
                            If the product received does not match the description or your expectations, please contact our Customer Service within 3-5 days. After evaluating your complaint, an appropriate action will be taken.
                        </Typography>
                    </ListItem>

                    <ListItem
                        sx={{ color: headingText }}
                        className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
                    >
                        <Typography component="span">
                            5. Warranty Claims:
                        </Typography>
                        <Typography className="pt-1 font-[13px] pl-4" style={{ color: subTitleText }}>
                            For products covered under manufacturer warranties, please directly contact the manufacturer for assistance.
                        </Typography>
                    </ListItem>

                    <ListItem
                        sx={{ color: headingText }}
                        className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
                    >
                        <Typography component="span">
                            6. Refunds:
                        </Typography>
                        <Typography className="pt-1 font-[13px] pl-4" style={{ color: subTitleText }}>
                            Once a refund is approved by our team, it will be processed within 3-5 business days.
                        </Typography>
                    </ListItem>
                </List>
            </PageLayout>
        </main>
    );
}

export default RefundCancellationPolicy;
