"use client";

import PlanDataGrid from "@/components/DataGrid";
import { <PERSON><PERSON><PERSON>old, DoubleColumnCompare } from "@/constants/icons";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import styles from "../pricing.module.css";
import { PlanObj } from "@/redux/slices/pricingSlice";
import { DATABASE_FEAT, SERVER_FEAT } from "../constant";
import Link from "next/link";
import {
  DASHBOARD_LINK,
  REDIRECT_ARROW,
  SOLAR_MEDAL_RIBBON_START_BOLD,
  SOLAR_MEDAL_STAR_BOLD,
  SOLAR_MODAL_STAR_SQUARE_BOLD,
} from "@/constants/vars";
import Image from "next/image";

interface Props {
  rows: PlanObj[];
  title: String;
  expanded?: boolean;
  type: "SERVER" | "DATABASE";
}

interface FeatItem {
  id: number;
  title: string;
}

function PlanTable(props: Props) {
  const { rows, title, expanded = false, type } = props;

  const [isExpanded, setIsExpanded] = useState(false);

  const [feats, setFeats] = useState<FeatItem[]>([]);

  const [isUsd, setIsUsd] = useState(true);

  const handleAccordionClick = () => setIsExpanded((prev) => !prev);

  useEffect(() => {
    if (type === "SERVER") setFeats(SERVER_FEAT);
    if (type === "DATABASE") setFeats(DATABASE_FEAT);
  }, [type]);

  useEffect(() => {
    setIsExpanded(expanded);
  }, []);

  const handleMoreClick = () => {
    window.open(DASHBOARD_LINK, "_blank");
  };

  return (
    <Box className="flex flex-col w-full md:w-[80%] lg:w-[85%] items-start justify-start gap-6">
      <Box className="flex items-start justify-start">
        <Typography className="text-tx-primary font-manrope text-xl font-semibold">
          {title}
        </Typography>
      </Box>

      <Box
        className="flex flex-col w-full justify-center items-center p-2 rounded-2xl gap-2 bg-bg-secondary h-full"
        sx={{
          border: "1px solid #303030",
        }}
      >
        <Box className="grid grid-cols-1 md:grid-cols-3 gap-2 justify-center items-center w-full">
          <Box className="grid grid-cols-1 items-start gap-1">
            <Box className="grid grid-cols-2 md:grid-cols-3 justify-between items-start bg-bg-tertiary p-5 gap-3 md:gap-5 rounded-xl">
              <Box className="grid md:col-span-2 justify-between items-start gap-3 md:gap-6">
                <Typography className="text-tx-primary text-base font-medium">
                  Starting from
                </Typography>
                <Box className="flex justify-start items-center gap-1">
                  <Typography className="text-br-secondary  font-manrope text-[26px] font-semibold">
                    ${(rows[0]?.compute_cost_per_hour * 720 || 20)?.toFixed(0)}
                  </Typography>
                  <Typography className="text-tx-secondary font-manrope text-base font-medium pt-2">
                    /Per month
                  </Typography>
                </Box>
              </Box>
              <Box className="grid col-span-1 text-right text-sm text-tx-primary font-manrope font-medium">
                For Individual developers and small teams.
              </Box>
            </Box>
            <Link
              href={DASHBOARD_LINK}
              passHref={true}
              target="_blank"
              className="w-full"
              title="Get Started"
            >
              <Button
                sx={{ width: "100%" }}
                className={
                  styles.button + " py-6 font-manrope font-semibold text-lg"
                }
              >
                Get Started
              </Button>
            </Link>
          </Box>
          {type === "SERVER" && (
            <Box className="grid col-span-2 grid-cols-1 md:grid-cols-2 items-center justify-center h-full bg-bg-tertiary rounded-xl gap-3 md:py-3 md:pt-4 md:px-5 p-5">
              <Box className="w-full flex flex-col justify-start h-full items-start rounded-xl">
                <Box className={"mb-[10px]"}>
                  <Typography
                    fontSize={"20px"}
                    fontWeight={"400"}
                    lineHeight={"30px"}
                    className="font-dmsans"
                  >
                    <span className={"text-[#9DFF00]"}>*ZERO</span> Platform
                    Fees, Pay only for the Compute! 🚀
                  </Typography>
                </Box>
                <Box>
                  <Typography
                    fontWeight={"400"}
                    fontSize={"14px"}
                    lineHeight={"22.4px"}
                    className="font-manrope"
                  >
                    Meaning you get all the features that Kuberns has to offer
                    for free forever.You need to pay only for the resources your
                    project uses (servers, Database....)
                  </Typography>
                </Box>
              </Box>
              <Box className="w-full flex flex-col justify-between items-start gap-2">
                <Box
                  className="px-[10px] py-[10px] flex items-start gap-3 bg-gradient-to-r from-[#191919] to-[#111] max-w-fit"
                  borderRadius={"10px"}
                  border={"1px dashed #383838;"}
                >
                  <Image
                    width={20}
                    height={20}
                    src={SOLAR_MEDAL_RIBBON_START_BOLD}
                    alt="SOLAR_MEDAL_RIBBON_START_BOLD"
                  />
                  <Typography
                    fontSize={"14px"}
                    fontWeight={500}
                    className="font-manrope"
                  >
                    AI Powered Deployments
                  </Typography>
                </Box>
                <Box
                  className="px-[10px] py-[10px] flex items-center gap-3 bg-gradient-to-r from-[#191919] to-[#111] max-w-fit"
                  borderRadius={"10px"}
                  border={"1px dashed #383838;"}
                >
                  <Image
                    width={20}
                    height={20}
                    src={SOLAR_MEDAL_STAR_BOLD}
                    alt="SOLAR_MEDAL_STAR_BOLD"
                  />
                  <Typography
                    fontSize={"14px"}
                    fontWeight={500}
                    className="font-manrope"
                  >
                    Real-time Monitoring & Alerts
                  </Typography>
                </Box>
                <Box className="flex flex-[3] items-center justify-center gap-3">
                  <Box
                    className="flex-[2.5] px-[10px] py-[10px] flex items-center gap-3 bg-gradient-to-r from-[#191919] to-[#111] max-w-fit"
                    borderRadius={"10px"}
                    border={"1px dashed #383838;"}
                  >
                    <Image
                      width={20}
                      height={20}
                      src={SOLAR_MODAL_STAR_SQUARE_BOLD}
                      alt="SOLAR_MODAL_STAR_SQUARE_BOLD"
                    />
                    <Typography
                      fontSize={"14px"}
                      fontWeight={500}
                      className="font-manrope"
                    >
                      Zero Configuration
                    </Typography>
                  </Box>
                  <Box className="flex flex-[0.5] gap-3">
                    <Typography>& more</Typography>
                    <Image
                      className="cursor-pointer"
                      width={14}
                      height={14}
                      src={REDIRECT_ARROW}
                      alt="redirect-arrow"
                      onClick={handleMoreClick}
                    />
                  </Box>
                </Box>
              </Box>
            </Box>
          )}
          {type === "DATABASE" && (
            <Box className="grid col-span-1 grid-cols-1 md:col-span-2 md:grid-cols-2 justify-center h-full items-start bg-bg-tertiary p-4 md:py-3 md:px-5 rounded-xl">
              {feats.map((feat: FeatItem) => {
                return (
                  <Box className="p-3 flex gap-4" key={feat.id}>
                    <BoltBold />
                    <Typography className="text-tx-primary font-manrope font-medium text-[15px]">
                      {feat.title}
                    </Typography>
                  </Box>
                );
              })}
            </Box>
          )}
        </Box>
        <Accordion
          className={styles.myAccordion}
          onChange={handleAccordionClick}
          disableGutters
          sx={{
            "&::before": {
              height: "0px", // Remove the border for all states
            },
          }}
          defaultExpanded={expanded}
        >
          <AccordionSummary
            className={styles.myAccordionSummary}
            sx={{
              "& .MuiAccordionSummary-content": {
                margin: "0px !important",
              },
            }}
          >
            <Box className="w-full flex relative justify-start md:justify-center items-center bg-[#1f1f1f] hover:bg-[#222] duration-300 ease-in-out p-2.5 rounded-xl gap-3 select-none cursor-pointer">
              <DoubleColumnCompare />
              <Typography className="text-[#A0A0A0] text-base font-bold font-manrope uppercase">
                Compare Plans
              </Typography>
              <Box className="absolute flex gap-5 right-6 top-[25%] text-[#A0A0A0] text-sm duration-300 ease-in-out items-center">
                <ToggleButtonGroup
                  color="primary"
                  value={isUsd ? "usd" : "inr"}
                  exclusive
                  onChange={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setIsUsd((prev) => !prev);
                  }}
                  aria-label="Platform"
                >
                  <ToggleButton className={`py-0 ${isUsd ? "text-br-secondary bg-bg-primary" : ""} `} value="usd" >
                    USD
                  </ToggleButton>
                  <ToggleButton className={`py-0 ${!isUsd ? "text-br-secondary bg-bg-primary" : ""} `} value="inr">
                    INR
                  </ToggleButton>
                </ToggleButtonGroup>
                <p className="hidden md:flex">{isExpanded ? "Hide Details" : "Show Details"}</p>
              </Box>
            </Box>
          </AccordionSummary>
          <AccordionDetails
            sx={{
              padding: "0px !important",
            }}
          >
            <Box className="h-[350px] w-full bg-[#161616]">
              <PlanDataGrid
                rows={rows}
                isLastRowBlur={false}
                type={type}
                isUsd={isUsd}
              />
            </Box>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Box>
  );
}

export default PlanTable;
