import { Box, Typography } from "@mui/material";
import React from "react";
import styles from "../pricing.module.css";
import { KnobBold } from "@/constants/icons";
import { Manrope } from "next/font/google";

interface Props {
  title: string;
  price: string;
  per: string;
  description: string;
} 

function PricingCard(props: Props) {
  return (
    <Box
      className={`w-full flex flex-col justify-start items-center p-4 md:p-5 rounded-3xl md:rounded-2xl gap-4 bg-bg-secondary font-manrope`}
      sx={{
        border: "1px solid #2C2C2C",
      }}
    >
      <Box className="flex justify-center items-center w-full gap-3">
        <KnobBold />
        <Typography className="text-tx-primary text-base md:text-lg font-semibold font-manrope">
          {props.title}
        </Typography>
      </Box>
      <span
        style={{
          width: "100%",
          border: "1px dashed #474747",
        }}
      />
      <Box className="flex flex-col md:flex-row justify-between items-center md:items-start w-full gap-4 md:gap-10">
        <Box className="flex flex-col w-full md:w-1/2 justify-center items-center bg-[#1F1F1F] gap-2 md:gap-3 py-4 px-4 md:py-5 md:px-7 rounded-xl ">
          <Typography className="text-tx-primary text-sm md:text-base">
            Starting from
          </Typography>
          <Typography className="text-br-secondary font-manrope text-2xl md:text-3xl font-semibold">
            {"$" + props.price}
          </Typography>
          <Typography className="text-tx-secondary text-sm">
            {props.per}
          </Typography>
        </Box>
        <Typography className="flex text-tx-primary text-right text-sm md:text-base font-manrope">
          {props.description}
        </Typography>
      </Box>
    </Box>
  );
}

export default PricingCard;
