import { WidgetBold } from "@/constants/icons";
import { Box, Typography } from "@mui/material";
import React from "react";

function FeatureCard({ title, isLast }: { title: string; isLast?: boolean }) {
  return (
    <Box
      className={`w-full flex justify-start items-center gap-5 px-3 md:px-5 py-3 md:py-4 rounded-xl bg-bg-secondary select-none cursor-auto ${
        isLast ? "col-start-2" : ""
      }`}
      sx={{
        border: "1px solid #2C2C2C",
      }}
    >
      <WidgetBold />
      <Typography className="text-tx-primary font-manrope font-medium text-sm md:text-base">
        {title}
      </Typography>
    </Box>
  );
}

export default FeatureCard;
