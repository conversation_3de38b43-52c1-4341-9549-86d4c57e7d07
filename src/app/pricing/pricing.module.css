.bgImage {
  position: relative;
  min-height: 80vh;
  background: none;
}

.bgImage::before {
  content: "";
  position: absolute;
  top: -120px;
  width: 100%;
  height: 1016.945px;
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/filler.svg");
  background-size: contain;
  background-position: top;
  background-repeat: no-repeat;
  z-index: -1;
}

.bgNoiseImage {
  position: relative;
  min-height: 80vh;
  background: none;
  overflow: hidden;
  /* Ensures no overflow from children */
}

.bgNoiseImage::before {
  content: "";
  position: absolute;
  width: 100vw;
  height: 100vh;
  background-image: radial-gradient(circle at center 80%,
      rgba(0, 0, 0, 0) 40%,
      rgba(0, 0, 0, 1) 100%);
    /* url("https://dh0pnjwiz7wby.cloudfront.net/public/gridbg.svg"); */

  background-size: 100%, 100%;
  /* Ensure gradient and image align */
  background-position: center, center;
  background-repeat: no-repeat, no-repeat;
  z-index: -1;
  opacity: 0.7;
  /* Adjust opacity of the effect */
  mix-blend-mode: lighten;
  /* Makes it blend nicely */
}

@media (max-width: 767px) {
  .bgImage::before {
    background-size: 200%;
    background-position: top;
    top: -45px;
  }

  .bgNoiseImage::before {
    width: 1000px;
    background-image: radial-gradient(circle at center 20%,
        rgba(0, 0, 0, 0) 50%,
        rgba(0, 0, 0, 1) 100%);
      /* url("https://dh0pnjwiz7wby.cloudfront.net/public/gridbg.svg"); */
    background-position: right;
  }
}

.hero_p_text {
  background: radial-gradient(78.21% 78.21% at 50% 50%, #fff 0%, #999 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.curr_region {
  border: 1px solid #212121;
}

/* CustomAccordion.css */
.myAccordion {
  box-shadow: none !important;
  background-color: #161616 !important;
  width: 100% !important;
}

.myAccordionSummary {
  padding: 0 !important;
  border: none !important;
}

.myAccordionSummary.MuiAccordionSummary-content {
  margin: 0 !important;
}

.myAccordionSummary.Mui-expanded::before {
  opacity: 0 !important;
  /* Remove the border when expanded */
}

.pricing_card {
  border-radius: 16px;
  border: 1px solid #2c2c2c;
  background: #161616;
  display: flex;
  padding: 20px;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex: 1 0 0;
}

.button {
  padding: 8px 18px;
  color: #fff;
  cursor: pointer;
  border-radius: 12px;
  background-image: radial-gradient(ellipse at 50% 50%,
      #168ff9 0%,
      #001a59 100%);

  /* background-size: 200% 100%; */
  transition: all 1s;
  /* background-position: 0 0; */
  margin-right: -5px;
  z-index: 1000;
  /* border: 1px solid #0b57ad; */
  font-family: Manrope;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.button:hover {
  background-position: 100% 250%;
}