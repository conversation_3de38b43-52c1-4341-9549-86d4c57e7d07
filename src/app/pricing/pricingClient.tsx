"use client";
import Head from "next/head";
import PageLayout from "@/components/Layout";
import { GlobeBold } from "@/constants/icons";
import { getPlans, getRegions, Region } from "@/redux/slices/pricingSlice";
import { RootState, useAppDispatch } from "@/redux/store";
import {
  Box,
  InputAdornment,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import { Manrope } from "next/font/google";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import FeatureCard from "./components/FeatureCard";
import PlanTable from "./components/PlanTable";
import PricingCard from "./components/PricingCard";
import { PLAN_FEATURES } from "./constant";
import styles from "./pricing.module.css";

const manrope = Manrope({ subsets: ["latin"] });

function Pricing() {
  const dispatch = useAppDispatch();

  const regions = useSelector((state: RootState) => state.pricingSlice.regions);

  const plans = useSelector((state: RootState) => state.pricingSlice.plans);

  const [selectedRegion, setSelectedRegion] = useState<string>("");

  useEffect(() => {
    dispatch(getRegions());
  }, []);

  useEffect(() => {
    if (regions.length > 0) {
      setSelectedRegion(regions[0].kuberns_region_name);
    }
  }, [regions]);

  useEffect(() => {
    if (selectedRegion) {
      dispatch(
        getPlans({
          region: selectedRegion,
          type: "SERVER",
          subType:"web"
        })
      );
      dispatch(
        getPlans({
          region: selectedRegion,
          type: "DATABASE",
          subType:"MYSQL"
        })
      );
    }
  }, [selectedRegion]);

  return (
    <main
      className={`flex flex-col items-center ${manrope.className} ${styles.bgNoiseImage}`}
    >
      <PageLayout
        className={`w-full h-full flex justify-center items-center flex-col pt-28 md:pt-36 ${styles.bgImage} z-10`}
      >
        <Box
          className={"w-full flex flex-col justify-center items-center gap-4"}
        >
          <h2
            className={`${styles.hero_p_text} text-lg md:text-2xl lg:text-3xl font-medium font-manrope p-1`}
          >
            Choose a Plan, Pay Only for What You Need
          </h2>
          <h1
            className={`text-base lg:text-lg text-tx-secondary font-manrope font-medium text-center`}
          >
            Explore our flexible pricing plans designed to meet your budget and
            requirements.
          </h1>
          {regions.length > 0 && (
            <Select
              className={`${styles.curr_region} px-4 mt-5 flex justify-center items-center rounded-full bg-bg-secondary font-semibold text-sm`}
              value={selectedRegion}
              size="small"
              sx={{
                "& .MuiSelect-select": {
                  minHeight: "0px !important",
                  padding: "0px !important",
                },
                paddingRight: "0px !important",
              }}
              renderValue={() => {
                return (
                  <Box className="flex gap-2 justify-center font-manrope text-tx-primary items-center">
                    <GlobeBold />
                    {selectedRegion}
                  </Box>
                );
              }}
              IconComponent={() => null}
              onChange={(e) => setSelectedRegion(e.target.value)}
              inputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <GlobeBold />
                  </InputAdornment>
                ),
              }}
              color="primary"
            >
              {regions.map((region: Region) => (
                <MenuItem value={region.kuberns_region_name}>
                  {region.kuberns_region_name}
                </MenuItem>
              ))}
            </Select>
          )}
        </Box>

        <Box className="h-20" />

        <PlanTable
          rows={plans["SERVER"]}
          title={"Compute Pricing"}
          expanded={true}
          type="SERVER"
        />
        <Box className="h-10" />

        <PlanTable
          rows={plans["DATABASE"]}
          title={"Database Pricing"}
          type="DATABASE"
        />
        <Box className="h-10" />

        {/* <PlanTable rows={plans["REDIS"]} title={"Redis Pricing"} /> */}

        <Box className="h-20 md:h-40" />

        {/* Usage pricing */}
        <Box
          className={"w-full flex flex-col justify-center items-center gap-2.5"}
        >
          <Typography
            className={`${styles.hero_p_text} text-lg font-manrope font-semibold p-2 md:text-2xl lg:text-[28px]`}
          >
            Usage Pricing
          </Typography>
          <h1
            className={`text-base lg:text-lg text-[#939393] font-manrope font-medium text-center`}
          >
            Explore our flexible pricing plans designed to meet your budget and
            requirements.
          </h1>
        </Box>

        <Box className="h-20" />

        <Box className="w-full md:w-[80%] lg:w-[85%] flex gap-5 md:gap-10 flex-col md:flex-row">
          <PricingCard
            title="Volume"
            price="0.2"
            per="per GB per Month"
            description="For Individual developers and small teams"
          />
          <PricingCard
            title="Data Transfer"
            price="0.1"
            per="per GB"
            description="For Individual developers and small teams"
          />
        </Box>

        <Box className="h-10 md:h-20" />

        <Box
          className={"w-full flex flex-col justify-center items-center gap-5"}
        >
          <Typography
            className={`${styles.hero_p_text} font-manrope text-center font-medium text-lg md:text-2xl`}
          >
            Get It All, No Matter the Plan You Choose
          </Typography>
        </Box>

        <Box className="h-8 md:h-16" />

        <Box className="grid grid-cols-2 md:grid-cols-3 justify-center items-stretch w-full md:w-[80%] lg:w-[85%] gap-3 md:gap-5 pb-0 md:pb-10 content-center">
          {PLAN_FEATURES.map((feat, index) => {
            const isLastItem = index === PLAN_FEATURES.length - 1;
            return (
              <FeatureCard
                title={feat.title}
                key={feat.id}
                isLast={isLastItem}
              />
            );
          })}
        </Box>
      </PageLayout>
    </main>
  );
}

export default Pricing;
