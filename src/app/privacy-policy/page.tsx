import PageLayout from "@/components/Layout";
import { List, ListItem, Typography } from "@mui/material";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: {
    absolute:
      "Privacy Policy | Kuberns: Your AI-Cloud PaaS for Seamless Deployment",
  },
  description:
    "Kuberns offers simple, flexible pricing to meet the needs of every individual developer and every organization. Integrate GitHub, infinite CI/CD deployments and many more.",
  alternates: {
    canonical: "https://kuberns.com/privacy-policy",
  },
  robots: {
    index: false,
    follow: false,
  },
};

function PrivacyPolicy() {
  const headingText = "#FFF";
  const innerHeadingText = "#FFF";
  const subTitleText = "#CCCCCC";

  return (
    <main
      className={`flex flex-col items-center font-manrope pt-16 md:pt-0 h-full`}
    >
      <PageLayout className="w-11/12 flex justify-center items-center flex-col pt-10 md:pt-20 gap-2">
        <Typography className="text-tx-primary font-manrope font-semibold text-2xl md:text-3xl">
          Privacy Policy
        </Typography>
        <Typography pt={2}>
          At Kuberns, we value your privacy and are committed to protecting your
          personal information. This Privacy Policy explains how we collect,
          use, and safeguard your data when you visit our website and use our
          services.
        </Typography>
        <List component="ol">
          <ListItem
            sx={{ color: headingText }}
            className="p-0 pt-3 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">1. Information We Collect:</Typography>
            <ul
              className="pt-1 font-semibold pl-4"
              style={{ color: subTitleText }}
            >
              <li className="font-[13px] p-0 pl-5">
                Personal Information: Name, email address, phone number, and
                other contact details when you sign up or contact us.
              </li>
              <li className="font-[13px] p-0 pl-5">
                Usage Data: IP address, browser type, pages visited, and
                interaction with our website for analytics and security.
              </li>
              <li className="font-[13px] p-0 pl-5">
                Payment Information: If you subscribe to our services, payment
                details may be processed securely through third-party payment
                providers.
              </li>
            </ul>
          </ListItem>

          <ListItem
            sx={{ color: headingText }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">
              2. How We Use Your Information:
            </Typography>
            <ul
              className="pt-1 font-semibold pl-4"
              style={{ color: subTitleText }}
            >
              <li className="font-[13px] p-0 pl-5">
                Provide and improve our PaaS deployment platform.
              </li>
              <li className="font-[13px] p-0 pl-5">
                Communicate important updates, promotions, or support messages.
              </li>
              <li className="font-[13px] p-0 pl-5">
                Enhance security and prevent fraudulent activities.
              </li>
              <li className="font-[13px] p-0 pl-5">
                Analyze user behavior to improve our services.
              </li>
            </ul>
          </ListItem>

          <ListItem
            sx={{ color: headingText }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">
              3. Data Security and Privacy:
            </Typography>
            <ul
              className="pt-1 font-semibold pl-4"
              style={{ color: subTitleText }}
            >
              <li className="font-[13px] p-0 pl-5">
                We do not sell or share your personal information with third
                parties for marketing purposes.
              </li>
              <li className="font-[13px] p-0 pl-5">
                We may share data with trusted service providers for hosting,
                analytics, and payment processing.
              </li>
              <li className="font-[13px] p-0 pl-5">
                We implement industry-standard security measures to protect your
                data.
              </li>
            </ul>
          </ListItem>

          <ListItem
            sx={{ color: headingText }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">
              4. Cookies and Tracking Technologies:
            </Typography>
            <Typography
              className="pt-1 font-[13px] pl-4"
              style={{ color: subTitleText }}
            >
              We use cookies and tracking tools to enhance user experience and
              analyze traffic. You can manage cookie preferences in your browser
              settings.
            </Typography>
          </ListItem>

          <ListItem
            sx={{ color: headingText }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">
              5. Your Rights and Choices:
            </Typography>
            <ul
              className="pt-1 font-semibold pl-4"
              style={{ color: subTitleText }}
            >
              <li className="font-[13px] p-0 pl-5">
                You can request access, modification, or deletion of your
                personal data.
              </li>
              <li className="font-[13px] p-0 pl-5">
                You may opt out of marketing emails anytime by clicking
                "unsubscribe."
              </li>
            </ul>
          </ListItem>

          <ListItem
            sx={{ color: headingText }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">6. Updates to This Policy:</Typography>
            <Typography
              className="pt-1 font-[13px] pl-4"
              style={{ color: subTitleText }}
            >
              We may update this Privacy Policy periodically. Please review it
              for any changes.
            </Typography>
          </ListItem>

          <ListItem
            sx={{ color: headingText }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">7. Contact Us:</Typography>
            <Typography
              className="pt-1 font-[13px] pl-4"
              style={{ color: subTitleText }}
            >
              For questions or concerns regarding this policy, contact us at
              <EMAIL>.
            </Typography>
          </ListItem>
        </List>
      </PageLayout>
    </main>
  );
}

export default PrivacyPolicy;
