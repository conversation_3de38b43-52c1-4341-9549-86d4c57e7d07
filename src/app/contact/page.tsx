import PageLayout from "@/components/Layout";
import { SocialLinks } from "@/constants/socialLinks";
import { CALENDLY_URL } from "@/constants/vars";
import { Box, Divider } from "@mui/material";
import Link from "next/link";
import HighlightWords from "../HomeComponent/WordHighlight";
import { Metadata } from "next";
import { FaMapLocationDot } from "react-icons/fa6";

export const metadata: Metadata = {
  title: {
    absolute: "Contact | Kuberns: Your AI-Cloud PaaS for Seamless Deployment",
  },
  description:
    "Kuberns offers simple, flexible pricing to meet the needs of every individual developer and every organization. Integrate GitHub, infinite CI/CD deployments and many more.",
  alternates: {
    canonical: "https://kuberns.com/contact",
  },
  robots: {
    index: true,
    follow: true,
  },
};

function Contact() {
  return (
    <main className={`flex flex-col items-center`}>
      <PageLayout
        className={`w-full flex justify-center items-center flex-col`}
      >
        <div className="flex items-center lg:items-start flex-col lg:flex-row justify-center w-full gap-5 pt-20 md:pt-6 lg:pt-20 xl:pt-24 lg:px-5 h-full">
          <iframe
            src={CALENDLY_URL}
            height="750px"
            className="rounded-xl p-0 m-0 w-full lg:w-3/5 xl:w-[60%] border border-dashed border-bg-primary"
          />
          <div className="rounded-2xl w-full lg:w-3/5 xl:w-[40%] border-2 border-dashed border-bg-tertiary p-6 md:p-12 gap-5 flex flex-col">
            <div className="grid grid-cols-2 gap-5">
              {SocialLinks.map((item) => {
                return (
                  <Link
                    href={item.url}
                    key={item.title}
                    target="_blank"
                    className="flex gap-2 items-center text-sm md:text-lg min-w-fit"
                    title={item.title}
                  >
                    {item.icon} {item.title}
                  </Link>
                );
              })}
            </div>
              {/* <Box className="flex gap-2 items-start text-sm md:text-lg min-w-fit">
                <FaMapLocationDot color="#CACACA" size={20} />
                B-100, Alap century, kalavad road, Rajkot, Gujarat - 360005
              </Box> */}
            <Divider />
            <HighlightWords
              text="Having trouble or need assistance within Kuberns? We’re here to support you! Just go to the Support section in the app, click on 'New Ticket', and share the details. Let’s work together to resolve it!"
              wordsToHighlight={["Support section", "New Ticket"]}
              highlightColor="#9E9E9E"
              className="text-[#9E9E9E] font-normal text-sm md:text-lg "
              highlightClassName="text-[#9E9E9E] font-bold text-sm md:text-lg"
            />
          </div>
        </div>
      </PageLayout>
    </main>
  );
}

export default Contact;
