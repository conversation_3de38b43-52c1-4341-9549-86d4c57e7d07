.bgImage {
  position: relative;
  min-height: 80vh;
  background: none;
}

.bgImage::before {
  content: "";
  position: absolute;
  top: -100px;
  width: 100%;
  height: 100%;
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/filler.svg");
  background-size: contain;
  background-position: top;
  background-repeat: no-repeat;
  z-index: -1;
}

.bgNoiseImage {
  position: relative;
  min-height: 80vh;
  background: none;
  overflow: hidden;
  /* Ensures no overflow from children */
}

.bgNoiseImage::before {
  content: "";
  position: absolute;
  width: 60vw;
  height: 60vh;
  /* background-image: radial-gradient(circle at center 80%,
      rgba(0, 0, 0, 0) 40%,
      rgba(0, 0, 0, 1) 100%), */
  /* url("https://dh0pnjwiz7wby.cloudfront.net/public/bgGrid.svg"); */

  background-size: 100%, 100%;
  /* Ensure gradient and image align */
  background-position: center, center;
  background-repeat: no-repeat, no-repeat;
  z-index: -1;
  opacity: 0.7;
  /* Adjust opacity of the effect */
  mix-blend-mode: lighten;
  /* Makes it blend nicely */
}

.emp_dev_text {
  display: inline-block;
  color: #cbd6e0;
  text-align: center;
  text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-family: Manrope;
}

.eli_dev_ops_text {
  background: linear-gradient(to right, #1f26b7 5%, #63b4c5 32%, #5d7fb6 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.box {
  position: relative;
  display: flex;
  background-color: #161616;
  border-radius: 30px;
  border-radius: 36px;
  border: 1px solid #202325;
  background: linear-gradient(45deg,
      rgba(253, 250, 241, 0.15) 14.64%,
      rgba(237, 204, 114, 0.15) 30.22%,
      rgba(226, 183, 72, 0.15) 36.34%,
      rgba(212, 164, 58, 0.15) 39.84%,
      rgba(221, 238, 253, 0.15) 61.39%,
      rgba(126, 174, 217, 0.15) 76.86%,
      rgba(73, 134, 190, 0.15) 85.36%);
  backdrop-filter: blur(4px);
}

.box::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 30px;
  padding: 1.5px;
  pointer-events: none;
}

.button {
  padding: 10px 30px;
  border: none;
  color: white;
  cursor: pointer;
  outline: none;
  font-size: 15px;
  border-radius: 25px;
  background-image: linear-gradient(to right, #2758d1 15%, #172285 45%);
  background-size: 200% 100%;
  transition: background-position 1s;
  z-index: 1000;
  background-position: 0 0;
}

.button:hover {
  background-position: 100% 250%;
}

.play_again_button {
  padding: 10px 30px;
  border: none;
  cursor: pointer;
  color: white;
  outline: none;
  font-size: 15px;
  border-radius: 25px;
  background: #808080;
  display: flex;
  justify-content: center;
  align-items: center;
}

.play_again_button:hover {
  background: #606060;
}

.upsale_button {
  position: relative;
  z-index: 0;
  height: 47px;
  overflow: hidden;
  cursor: pointer;
  border: none !important;
  padding: 0 !important;
  background: #111111 !important;
  color: #656565;
  border-radius: 25px;
  width: 260px;
  min-width: fit-content;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 50px 0 rgb(0 0 0 / 7%);
  transition: 0.2s all linear;
  text-decoration: initial;
  z-index: 1000;
}

.upsale_button:hover {
  background-position: 100% 250%;
}

.upsale_button span {
  position: relative;
  z-index: 1;
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  top: 2px;
  left: 2px;
  align-items: center;
  display: flex;
  justify-content: center;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 25px;
  background-image: linear-gradient(to right, #2758d1 15%, #172285 45%);
  background-size: 200% 100%;
  transition: background-position 1s;
  background-position: 0 0;
}

.upsale_button:after {
  content: "";
  position: absolute;
  z-index: -2;
  left: -50%;
  top: -50%;
  width: 200%;
  height: 200%;
  background-color: transparent;
  background-repeat: no-repeat;
  background-size: 50% 50%, 50% 50%;
  background-position: 0 0, 100% 0, 100% 100%, 0 100%;
  background-image: linear-gradient(90deg,
      rgba(255, 255, 255, 0) 0%,
      #3c6ce4 50%,
      rgba(255, 255, 255, 0) 100%);
  -webkit-animation: rotate 3s linear infinite;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

.intro_vdo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  aspect-ratio: 16/9;
  border: 0px;
  border-radius: 20px 20px 0px 0px;
}

.floating_btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #3b82f6;
  box-shadow: 0 4px 50px 0 rgb(0 0 0 / 7%);
  border: none;
  outline: none;
  padding: 0;
}

.floating_btn:hover {
  background-color: #1e40af;
}

.world_map {
  width: 85%;
  height: 85%;
  object-fit: contain;
  object-position: center;
  aspect-ratio: 16 / 9;
}

.active_step {
  position: relative;
  z-index: 0;
  height: 45px;
  width: 210px;
  overflow: hidden;
  cursor: pointer;
  border: none !important;
  padding: 0 !important;
  background: #0e0e0e !important;
  color: #656565;
  border-radius: 25px;
  min-width: fit-content;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 50px 0 rgb(0 0 0 / 7%);
  transition: 0.2s all linear;
  text-decoration: initial;
}

.active_step span {
  position: relative;
  z-index: 1;
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  top: 2px;
  left: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 20.25px;
  /* border: 1px dashed #2b2b2b; */
  background: radial-gradient(50% 50% at 50% 50%, #222 0%, #111 100%);
  backdrop-filter: blur(6.074999809265137px);
}

.active_step:after {
  content: "";
  position: absolute;
  z-index: -2;
  left: -50%;
  top: -50%;
  width: 200%;
  height: 200%;
  background-color: transparent;
  background-repeat: no-repeat;
  background-size: 50% 50%, 50% 50%;
  background-position: 0 0, 100% 0, 100% 100%, 0 100%;
  background-image: linear-gradient(90deg,
      rgba(255, 255, 255, 0) 0%,
      #3c6ce4 50%,
      rgba(255, 255, 255, 0) 100%);
  -webkit-animation: rotate 3s linear infinite;
  animation: rotate 3s linear infinite;
}

.inActive_step {
  position: relative;
  z-index: 0;
  height: 45px;
  width: 210px;
  overflow: hidden;
  cursor: pointer;
  border: none !important;
  padding: 0 !important;
  color: #656565;
  border-radius: 25px;
  min-width: fit-content;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 50px 0 rgb(0 0 0 / 7%);
  transition: 0.2s all linear;
  text-decoration: initial;
}

.inActive_step span {
  position: relative;
  z-index: 1;
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  top: 2px;
  left: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cacaca;
  border-radius: 20.25px;
  border: 1px dashed #2b2b2b;
  background: radial-gradient(50% 50% at 50% 50%, #222 0%, #111 100%);
  backdrop-filter: blur(6.074999809265137px);
}

.detail_plan_button {
  padding: 5px 30px;
  border: none;
  color: white;
  cursor: pointer;
  outline: none;
  font-size: 15px;
  border-radius: 25px;
  background-image: linear-gradient(to right, #00084f 15%, #172285 50%);
  background-size: 200% 100%;
  transition: background-position 1s;
  z-index: 1000;
  background-position: 0 0;
}

.detail_plan_button:hover {
  background-position: 100% 250%;
}

.get_started {
  padding: 25px;
  border: none;
  color: white;
  cursor: pointer;
  outline: none;
  font-size: 16px !important;
  border-radius: 10px;
  background-image: linear-gradient(to right, #2758d1 15%, #172285 45%);
  background-size: 200% 100%;
  transition: background-position 1s;
  z-index: 1000;
  background-position: 0 0;
}

.get_started:hover {
  background-position: 100% 250%;
}

.view_details {
  padding: 8px 25px;
  color: white;
  cursor: pointer;
  outline: none;
  font-size: 16px !important;
  border-radius: 10px;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5;
  background-color: #1f1f1f;
}

.connect_github {
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/home_last_section.png");
  background-size: contain;
  background-repeat: repeat-x;
  background-position: center;
  color: #cacaca;
  width: 100%;
  height: 90%;
  background-color: #161616;
  border-radius: 24px;
  border: 1px solid #2c2c2c;
  background-blend-mode: color-burn, normal;
}

.connect_github_button {
  border-radius: 12px;
  border: 1px solid #2a2a2a;
  background: #1f1f1f;
  transition: 0.3s all ease-in-out;
}

.button_with_icon {
  position: relative;
  border: 1px solid #2b2b2b;
  background-image: radial-gradient(50% 50% at 50% 50%, #222 0%, #080808 100%);
  backdrop-filter: blur(6.074999809265137px);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  font-family: Manrope;
  transition: 0.3s all ease-in-out;
}

.button_with_icon:hover {
  scale: 1.05;
}

.button_with_icon_text {
  background-image: conic-gradient(from 180deg at 48.5% 50%,
      #fbfbfd 26.24999910593033deg,
      #c8d4da 88.12500178813934deg,
      #fff 156.58468008041382deg,
      #aec0ce 191.74442768096924deg,
      #e3e9ee 237.1290135383606deg,
      #fafbfc 255.19062280654907deg,
      #d6dfe6 310.1085305213928deg,
      #b8c9d3 331.875deg);
  background-clip: text;
}

.button_with_icon_italic_text {
  background: conic-gradient(from 180deg at 48.5% 50%,
      #fbfbfd 26.24999910593033deg,
      #c8d4da 88.12500178813934deg,
      #fff 156.58468008041382deg,
      #aec0ce 191.74442768096924deg,
      #e3e9ee 237.1290135383606deg,
      #fafbfc 255.19062280654907deg,
      #d6dfe6 310.1085305213928deg,
      #b8c9d3 331.875deg);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: "DM Serif Text";
  font-size: 16px;
  font-style: italic;
  font-weight: 600;
}

.connect_github_button:hover {
  background: #2f2f2f;
}

@media (max-width: 1023px) {
  .get_started {
    font-size: 15px;
    padding: 5px 20px;
  }
}

@media (max-width: 767px) {
  .button {
    padding: 5px 15px;
    font-size: 13px;
  }

  .bgImage::before {
    background-size: 200%;
    background-position: top;
    top: 0;
  }

  .inActive_step {
    width: 150px;
  }

  .intro_vdo {
    border-radius: 10px 10px 0px 0px;
  }

  .connect_github {
    background-size: cover;
  }

  .bgNoiseImage::before {
    width: 1000px;
    /* background-image: radial-gradient(circle at center 20%,
        rgba(0, 0, 0, 0) 50%,
        rgba(0, 0, 0, 1) 100%),
      url("https://dh0pnjwiz7wby.cloudfront.net/public/bgGrid.svg"); */
    background-position: right;
  }
}

.why_kuberns_btn {
  border-radius: 36px;
  border: 1px dashed #2a2a2a;
  background: #111;
  backdrop-filter: blur(6.074999809265137px);
  display: flex;
  padding: 8px 30px;
  align-items: center;
  gap: 6.48px;
  font-family: Manrope;
  transition: 0.3s all ease-in-out;
}

.why_kuberns_btn:hover {
  background: #222;
  border-style: solid;
}