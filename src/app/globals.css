@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  padding: 0;
  margin: 0;
}

/* For Chrome, Edge, and Safari */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #111111;
}

::-webkit-scrollbar-thumb {
  background-color: #444;
  border-radius: 20px;
  border: 3px solid #111111;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  /* "auto" or "thin" */
  scrollbar-color: #444 #111111;
  /* thumb and track color */
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(200%);
  }
}

/* Shimmer Effect */
.shimmer-effect {
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  pointer-events: none;
  animation: shimmer 3s infinite;
  z-index: 1;
  transition: opacity 0.5s ease-in-out;
}

button .shimmer-effect {
  opacity: 1;
}

.gradient_text {
  background: conic-gradient(from 180deg at 48.5% 50%,
      #fbfbfd 26.24999910593033deg,
      #c8d4da 88.12500178813934deg,
      #fff 156.58468008041382deg,
      #aec0ce 191.74442768096924deg,
      #e3e9ee 237.1290135383606deg,
      #fafbfc 255.19062280654907deg,
      #d6dfe6 310.1085305213928deg,
      #b8c9d3 331.875deg);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.heading_text {
  background: conic-gradient(from 180deg at 48.5% 50%,
      #fbfbfd 26.24999910593033deg,
      #c8d4da 88.12500178813934deg,
      #fff 156.58468008041382deg,
      #aec0ce 191.74442768096924deg,
      #e3e9ee 237.1290135383606deg,
      #fafbfc 255.19062280654907deg,
      #d6dfe6 310.1085305213928deg,
      #b8c9d3 331.875deg);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.noiseBgImage {
  position: relative;
}

.noiseBgImage::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/CardBgNoise.svg");
  background-color: #111111;
  background-size: 4%;
  opacity: 0.2;
  /* border-radius: 16px; */
}

.hidden-heading {
  position: absolute;
  left: -9999px;
  top: -9999px;
  width: 1px;
  height: 1px;
  overflow: hidden;
  white-space: nowrap;
}