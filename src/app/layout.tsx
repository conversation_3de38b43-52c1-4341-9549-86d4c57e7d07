import MinimalFooter from "@/components/Footer/MinimalFooter";
import TopBar from "@/components/TopBar";
import { LOGO } from "@/constants/vars";
import { ReduxProvider } from "@/redux/provider";
import theme from "@/theme";
import { CssBaseline, ThemeProvider } from "@mui/material";
import type { Metadata } from "next";
import { Archivo, DM_Sans, DM_Serif_Text, Manrope } from "next/font/google";
import Script from "next/script";
import Navbar from "../components/nav";
import "./globals.css";
import { headers } from "next/headers";

export const metadata: Metadata = {
  metadataBase: new URL("https://kuberns.com"),
  title: {
    default: "Kuberns: One-Click AI Cloud Deployment",
    template: "%s | Kuberns", // This allows child pages to append to the title
  },
  description:
    "Kuberns is an AI-powered cloud Platform as a Service (PaaS) that simplifies cloud deployment, scaling, and management for seamless DevOps automation.",
  openGraph: {
    title: "Kuberns: One-Click AI Cloud Deployment",
    description:
      "Kuberns is an AI-powered cloud Platform as a Service (PaaS) that simplifies cloud deployment, scaling, and management for seamless DevOps automation.",
    url: "https://kuberns.com/",
    siteName: "Kuberns",
    images: [
      {
        url: "https://dh0pnjwiz7wby.cloudfront.net/public/logo.svg",
        width: 800,
        height: 418,
      },
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Kuberns: One-Click AI Cloud Deployment",
    description:
      "Kuberns is an AI-powered cloud Platform as a Service (PaaS) that simplifies cloud deployment, scaling, and management for seamless DevOps automation.",
    images: ["https://dh0pnjwiz7wby.cloudfront.net/public/logo.svg"],
    site: "@Kuberns_cloud",
  },
  publisher: "Kuberns",
  robots: {
    index: true,
    follow: true,
  },
};

const manrope = Manrope({
  subsets: ["latin"],
  variable: "--font-manrope",
  display: "swap",
});
const archivo = Archivo({
  subsets: ["latin"],
  variable: "--font-archivo",
  display: "swap",
});

const dmserif = DM_Serif_Text({
  subsets: ["latin"],
  weight: "400",
  variable: "--font-dmserif",
  display: "swap",
});

const dmsans = DM_Sans({
  subsets: ["latin"],
  variable: "--font-dmsans",
  display: "swap",
});

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // FIXME: Have to add this org. schema to proper place, currently hard codedly put here for testing
  // Organization Schema
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://kuberns.com/#organization",
    name: "Kuberns",
    legalName: "TECHERA",
    alternateName: "Kuberns",
    url: "https://kuberns.com",
    logo: "https://kuberns-landing-page.s3.ap-south-1.amazonaws.com/public/logo.svg",
    foundingDate: "2024-01-01",
    founder: {
      "@type": "Person",
      name: "Harsh Kanani",
      jobTitle: "CEO & Co-Founder",
      email: "<EMAIL>",
      sameAs: ["https://www.linkedin.com/in/harsh-kanani-69a45818b/"],
    },
    employee: [
      {
        "@type": "Person",
        name: "Jevin Vekaria",
        jobTitle: "CTO & Co-Founder",
        email: "<EMAIL>",
        sameAs: ["https://linkedin.com/in/jevin925"],
      },
    ],
    location: [
      {
        "@type": "Place",
        address: {
          "@type": "PostalAddress",
          streetAddress: "B-100 Alap Century",
          addressLocality: "Rajkot",
          addressRegion: "Gujarat",
          postalCode: "380005",
          addressCountry: "IN",
        },
      },
    ],
    telephone: "+91-8866190761",
    email: "<EMAIL>",
    contactPoint: [
      {
        "@type": "ContactPoint",
        contactType: "Customer Support",
        telephone: "+91-8866190761",
        email: "<EMAIL>",
        areaServed: "Worldwide",
      },
    ],
    sameAs: [
      "https://www.linkedin.com/company/kuberns",
      "https://x.com/Kuberns_cloud",
    ],
    numberOfEmployees: "10-50",
    areaServed: "Worldwide",
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Kuberns Cloud Services",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "PaaS Deployment",
            description:
              "Deploy your applications seamlessly with Kuberns managed cloud infrastructure.",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Custom Domains & SSL",
            description:
              "Easily configure custom domains with automatic SSL management.",
          },
        },
        {
          "@type": "Offer",
          itemOffered: {
            "@type": "Service",
            name: "Advanced Monitoring",
            description:
              "Real-time analytics, logs, and system health monitoring for your cloud applications.",
          },
        },
      ],
    },
  };

  // Canonical URL
  // const router = useRouter()
  // Use environment variable or fall back to a default
  const baseUrl = "https://kuberns.com";

  const headerList = await headers();

  const pathname = headerList
    .get("referer")
    ?.split("://")?.[1]
    ?.split("/")?.[1];

  const isAboutPage = pathname?.includes("about");

  const isPricingPage = pathname?.includes("pricing");

  // const pathname = usePathname();
  // const canonicalUrl = (baseUrl + pathname).split('?')[0]

  // // Build the canonical URL (removing query strings for cleaner SEO)
  // const canonicalUrl = (baseUrl + router.asPath).split('?')[0]

  return (
    <html
      lang="en"
      className={`${manrope.variable} ${dmsans.variable} ${archivo.variable} ${dmserif.variable}`}
    >
      <link rel="icon" href={LOGO} sizes="any" />
      <link
        rel="preload"
        as="image"
        href="https://dh0pnjwiz7wby.cloudfront.net/public/filler.svg"
        fetchPriority="high"
      />
      <link
        rel="preload"
        as="image"
        href="https://dh0pnjwiz7wby.cloudfront.net/public/gridbg.svg"
        fetchPriority="high"
      />

      <head>
        {!isAboutPage && (
          <h1 className="hidden-heading">
            AI-Powered Cloud Autopilot for One Click Deployments
          </h1>
        )}
        {!isAboutPage && !isPricingPage && (
          <h2 className="hidden-heading">
            Simplify, Automate, and Scale Your Cloud Deployments in minutes!
          </h2>
        )}
        <h3 className="hidden-heading">
          Why Choose Kuberns for Your Cloud Deployment?
        </h3>
        <h4 className="hidden-heading">
          Boundless Features Built to Save You Time & Money
        </h4>
        <h5 className="hidden-heading">
          Find the Perfect Pricing Plan for You
        </h5>
        {/* <h6 className="hidden-heading">What Our Customers Say About Kuberns</h6> */}

        {/* Embed JSON-LD */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema),
          }}
        />
        <Script
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=G-K5JXYSXWNH"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-K5JXYSXWNH');
      `}
        </Script>
        <Script
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=AW-16863873207"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'AW-16863873207');
      `}
        </Script>
        <Script
          id="posthog-script"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `!function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.crossOrigin="anonymous",p.async=!0,p.src=s.api_host.replace(".i.posthog.com","-assets.i.posthog.com")+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="init me ws ys ps bs capture je Di ks register register_once register_for_session unregister unregister_for_session Ps getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSurveysLoaded onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey canRenderSurveyAsync identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted captureException loadToolbar get_property getSessionProperty Es $s createPersonProfile Is opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing Ss debug xs getPageViewId captureTraceFeedback captureTraceMetric".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
              posthog.init('phc_ykUMPJp1dwWEmYxD16Utg95tqMSh2zNX7jPL9EZBNqW', {
                  api_host: 'https://us.i.posthog.com',
                  cookie_domain: '.kuberns.com',
                  person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
              });
            `,
          }}
        />

        {/* Facebook Pixel */}
        <Script
          id="facebook-pixel"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '1800301930731747');
              fbq('track', 'PageView');
            `,
          }}
        />

        <script
          type="text/javascript"
          dangerouslySetInnerHTML={{
            __html: `(function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "pyj2vbo965");`,
          }}
        />
        {/* <link rel="canonical" href={baseUrl} /> */}
      </head>
      <body
        style={{
          height: "100%",
          minHeight: "100vh",
        }}
      >
        {/* <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-P2T29XJN"
            height="0"
            width="0"
            style={{ display: 'none', visibility: 'hidden' }}
          ></iframe>
        </noscript> */}
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <ReduxProvider>
            <TopBar />
            <Navbar />
            {children}
            {/* <MinimalFooter /> */}
          </ReduxProvider>
        </ThemeProvider>
      </body>
      <script
        type="text/javascript"
        dangerouslySetInnerHTML={{
          __html: `
              _linkedin_partner_id = "6713850";
              window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
              window._linkedin_data_partner_ids.push(_linkedin_partner_id);
            `,
        }}
      ></script>
      <script
        type="text/javascript"
        dangerouslySetInnerHTML={{
          __html: `
              (function(l) {
                if (!l) {
                  window.lintrk = function(a,b) {
                    window.lintrk.q.push([a,b])
                  };
                  window.lintrk.q = [];
                }
                var s = document.getElementsByTagName("script")[0];
                var b = document.createElement("script");
                b.type = "text/javascript";
                b.async = true;
                b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
                s.parentNode.insertBefore(b, s);
              })(window.lintrk);
            `,
        }}
      ></script>
      <noscript>
        <img
          height="1"
          width="1"
          style={{ display: "none" }}
          title="Deploy your backend in one click with Kuberns"
          alt="Deploy your backend in one click with Kuberns"
          src="https://px.ads.linkedin.com/collect/?pid=6713850&fmt=gif"
        />
      </noscript>
      <noscript>
        <img
          height="1"
          width="1"
          style={{ display: "none" }}
          src="https://www.facebook.com/tr?id=1800301930731747&ev=PageView&noscript=1"
        />
      </noscript>
    </html>
  );
}
