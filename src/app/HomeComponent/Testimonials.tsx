import { SmileyFace } from "@/constants/icons";
import testiData from "@/constants/TestimonialData";
import { Box, Typography } from "@mui/material";
import styles from "../page.module.css";

function Testimonials() {
  return (
    <Box className="w-full flex flex-col justify-center items-center">
      <Box className="flex flex-col gap-3 items-center justify-center">
        <Typography className="text-br-secondary font-medium font-manrope flex items-center text-lg md:text-lg gap-1.5 md:gap-2.5">
          <SmileyFace /> What Our Users Say
        </Typography>
        <Typography
          className={`text-xl w-11/12 md:w-full font-manrope leading-relaxed text-center font-semibold md:text-[28px] ${styles.emp_dev_text}`}
        >
          Trusted by Developers, Loved by Teams
        </Typography>
      </Box>
      <Box className="h-6 md:h-16" />
      <Box
        className={`flex flex-col justify-center items-center w-full relative overflow-hidden px-2 md:px-0`}
      >
        <Box className="flex justify-center items-stretch gap-5 overflow-x-auto md:overflow-x-hidden w-full py-6 md:py-0 *:pr-5 sm:pr-[15%]">
          {testiData.slice(0, 7).map((testi) => (
            <TestiCard {...testi} />
          ))}
        </Box>
        <Box className="h-5" />
        <Box className="hidden md:flex justify-center items-stretch gap-5 overflow-auto sm:overflow-x-hidden pr-0 sm:pr-[15%] w-full">
          {testiData.slice(8).map((testi) => (
            <TestiCard {...testi} />
          ))}
        </Box>
        <Box className="absolute hidden md:flex w-1/5 h-full top-0 -left-[15%] bg-black/40 shadow-2xl blur-2xl" />
        <Box className="absolute hidden md:flex w-1/5 h-full top-0 -right-[15%] bg-black/40 shadow-2xl blur-2xl" />
      </Box>
    </Box>
  );
}

export default Testimonials;

interface CardProps {
  image?: string;
  name: string;
  position: string;
  text: string;
}

function TestiCard(props: CardProps) {
  const { image, name, position, text } = props;

  return (
    <Box className="w-full min-w-[300px] md:min-w-[500px] min-h-[250px] flex flex-col overflow-hidden rounded-2xl shadow-2xl border-dashed border-2 border-[#2A2A2A] group noiseBgImage">
      <Box className="flex flex-col gap-5 p-4 md:p-5">
        <Box className="h-[70%]">
          <Typography className="font-manrope duration-1000 font-medium md:leading-relaxed text-tx-primary ">
            "{text}"
          </Typography>
        </Box>
        <span className="w-full border border-dashed border-[#2A2A2A] flex" />
        <Box className="flex justify-start items-center gap-3">
          {image && (
            <Box className="flex justify-start items-center">
              <img
                src={image}
                alt={name}
                title="Deploy your backend in one click with Kuberns"
                height={75}
                width={75}
                className="aspect-square rounded-lg object-cover"
              />
            </Box>
          )}
          <Box className="flex flex-col md:gap-1">
            <Typography className="text-sm md:text-[15px] font-manrope font-medium text-tx-primary">
              {name}
            </Typography>
            <Typography className="text-sm md:text-[15px] font-manrope font-medium text-tx-secondary">
              {position}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
