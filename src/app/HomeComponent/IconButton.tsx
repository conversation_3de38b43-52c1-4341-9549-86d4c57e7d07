import React from "react";
import styles from "../page.module.css";

interface ButtonProps {
  url: string;
  text: string;
  italicText?: string;
  icon?: React.ReactNode;
  prefix?: boolean;
  suffix?: boolean;
}

function ButtonWithIcon({
  url,
  text,
  italicText,
  icon,
  prefix,
  suffix,
}: ButtonProps) {
  const handleRedirect = () => {
    window.open(url, "_blank");
  };

  return (
    <button
      onClick={handleRedirect}
      className={`flex flex-row items-center justify-center
      relative px-4 py-2.5 gap-2 rounded-full overflow-hidden
      ${styles.button_with_icon} z-50`}
    >
      {prefix && icon && icon}

      <div className="flex flex-row gap-1 overflow-visible px-1 text-sm md:text-base items-center justify-center gradient_text text-nowrap">
        {text && (
          <p className={`font-manrope`}>
            {text}
          </p>
        )}
        {italicText && (
            <p className={`font-medium font-dmserif italic tracking-wide`}>
            {italicText}
            </p>
        )}
      </div>
      {suffix && icon && icon}
    </button>
  );
}

export default ButtonWithIcon;
