import TypeWriterComp from "@/components/TypeWriter";
import {
  AWS,
  CALENDLY,
  CALENDLY_LINK,
  COIN2,
  CUP,
  GOOGLE_LINK,
} from "@/constants/vars";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import ButtonWithIcon from "../IconButton";
import HighlightWords from "../WordHighlight";
import styles from "./index.module.css";
import { HERO_TAG_LINES } from "@/constants/defaults/hero";
import HeroTagLine from "./components/tag-line";
import React from "react";
import DotSeparator from "@/constants/icons/DotSeparator";

function HeroHome() {
  return (
    <div className="flex flex-col w-full items-center justify-center pt-32">
      <div className="flex items-center justify-center gap-3">
        {HERO_TAG_LINES.map((tagline, index) => {
          return (
            <React.Fragment key={`tagline-${index}`}>
              <HeroTagLine {...tagline} />
              {index !== HERO_TAG_LINES.length - 1 && <DotSeparator />}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}

export default HeroHome;

{
  /* <div className="flex flex-col justify-center items-center w-screen py-20 h-full md:h-screen md:py-0">
      <img
        title="Deploy your backend in one click with Kuberns"
        src="https://dh0pnjwiz7wby.cloudfront.net/public/filler.svg"
        alt="filler"
        className="absolute top-0 left-0 w-screen h-screen pb-24 md:pb-0 md:p-16 opacity-80 object-fit -z-10 hidden md:block"
        loading="eager"
        fetchPriority="high"
      />

      <div
        className="absolute top-48 md:top-0 left-0 w-screen opacity-80 scale-120 md:scale-100 object-cover -z-50 hidden md:block"
        style={{
          backgroundImage: `url('https://dh0pnjwiz7wby.cloudfront.net/public/gridbg.svg')`,
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "top",
          height: "100vh",
        }}
      ></div>

      <Box
        className={`flex justify-center items-center gap-2 md:gap-3.5 rounded-sm md:rounded border-none p-1 w-fit ${styles.box} z-10`}
      >
        <Image
          src={AWS}
          width={30}
          height={30}
          className="h-auto w-5 md:w-6"
          alt="aws"
          title="Deploy your backend in one click with Kuberns"
        />
        <Typography
          className={`text-xs md:text-sm font-manrope text-[#D4D4D4] wide`}
        >
          Campus Fund • Winner
        </Typography>
        <Image
          src={CUP}
          width={20}
          height={20}
          className="hidden md:block"
          alt="award"
          title="Deploy your backend in one click with Kuberns"
        />
      </Box>

      <Box className="h-4" />
      <Box className="flex w-fit h-fit justify-center items-center flex-col gap-2 md:gap-3.5">
        <Typography
          className={`w-fit text-4xl font-normal md:font-light leading-snug md:leading-[125%] md:text-5xl xl:text-[58px] h-fit ${styles.emp_dev_text} font-manrope heading_text`}
        >
          AI-Powered Cloud Autopilot <br className="hidden md:block" /> You
          Deserve!
        </Typography>
      </Box>

      <Box className="h-3 md:h-6" />
      <Box className="flex w-10/12 md:w-[50%] justify-center items-center flex-col gap-5">
        <HighlightWords
          text="From code to production in minutes — experience AI Cloud-driven deployments that free your team to innovate - trusted by 1,000+ teams."
          wordsToHighlight={[]}
          highlightColor="#9E9E9E"
          className={`text-center text-sm md:text-lg font-dmsans md:leading-8 font-light text-tx-secondary text-wrap`}
        />
      </Box>


      <Box className="h-4 md:h-4" />
      <div className="w-full flex flex-col md:flex-row gap-3 justify-center items-center">
        <div className="flex flex-grow flex-col md:flex-row w-full justify-center items-center gap-2.5 md:gap-4">
          <ButtonWithIcon
            url={CALENDLY_LINK}
            text={"Schedule a"}
            italicText="Demo"
            icon={
              <Image
                src={CALENDLY}
                width={20}
                height={20}
                alt="calendly"
                title="Deploy your backend in one click with Kuberns"
              />
            }
            prefix={true}
          />

          <ButtonWithIcon
            url={GOOGLE_LINK}
            text={"Deploy like a PRO for"}
            italicText={"Free"}
            icon={
              <Image
                src={COIN2}
                width={20}
                height={20}
                alt="coin"
                title="Deploy your backend in one click with Kuberns"
              />
            }
            prefix={true}
          />
        </div>
      </div>

      <Box className="h-8 md:h-14" />

      <Box className="w-fit flex flex-col md:flex-row gap-2 md:gap-1 font-dmsans text-base md:text-lg justify-center items-center text-center mb-3">
        {" "}
        <Typography className="text-tx-secondary font-dmsans font-light px-1 text-sm md:text-lg ">
          With Kuberns Cloud AI, you can deploy
        </Typography>
        <div className="flex text-white flex-row gap-0 px-1.5 py-0 rounded-md bg-stone-950 border border-stone-900">
          <TypeWriterComp text={["Backend", "Frontend"]} />
        </div>
        <Typography className="text-tx-secondary font-dmsans font-light text-sm  md:text-lg ">
          in one click.
        </Typography>
      </Box>
    </div> */
}
