import { HeroTagLineType } from "@/constants/defaults/hero";
import cn from "@/helpers/cn";
import Image from "next/image";
import React from "react";

function HeroTagLine(props: HeroTagLineType) {
  const { icon, position, title, size, alt } = props;
  return (
    <div className={cn("flex items-center justify-center gap-3")}>
      {position === "prefix" && (
        <Image
          src={icon}
          width={size}
          height={size}
          alt={alt}
          title="Deploy your backend in one click with Kuberns"
        />
      )}

      <p className={`text-xs md:text-sm font-manrope text-tx-primary-1`}>
        {title}
      </p>
      {position === "suffix" && (
        <Image
          src={icon}
          width={size}
          height={size}
          className="h-auto w-5 md:w-6"
          alt={alt}
          title="Deploy your backend in one click with Kuberns"
        />
      )}
    </div>
  );
}

export default HeroTagLine;
