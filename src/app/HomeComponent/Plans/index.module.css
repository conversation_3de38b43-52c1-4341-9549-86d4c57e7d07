.button {
  padding: 8px 18px;
  color: #fff;
  cursor: pointer;
  border-radius: 12px;
  background-image: radial-gradient(ellipse at 50% 50%,
      #168ff9 0%,
      #001a59 100%);

  /* background-size: 200% 100%; */
  transition: all 1s;
  /* background-position: 0 0; */
  margin-right: -5px;
  z-index: 1000;
  border: 1px solid #0b57ad;
  font-family: Manrope;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.button:hover {
  background-position: 100% 250%;
}

.emp_dev_text {
  display: inline-block;
  color: #cbd6e0;
  text-align: center;
  text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-family: Manrope;
  font-size: 48px;
  font-style: normal;
  font-weight: 400;
}

.get_started {
  padding: 25px;
  border: none;
  color: white;
  cursor: pointer;
  outline: none;
  font-size: 16px !important;
  border-radius: 10px;
  background-image: linear-gradient(to right, #2758d1 15%, #172285 45%);
  background-size: 200% 100%;
  transition: background-position 1s;
  z-index: 1000;
  background-position: 0 0;
}

.get_started:hover {
  background-position: 100% 250%;
}

.view_details {
  padding: 8px 25px;
  color: white;
  cursor: pointer;
  outline: none;
  font-size: 16px !important;
  border-radius: 10px;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5;
  background-color: #1f1f1f;
}

.planBgImage {
  position: relative;
}

.planBgImage::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/CardBgNoise.svg");
  background-color: #111111;
  background-size: 4%;
  opacity: 0.2;
  border-radius: 16px 16px 0 0;
}

.planTableBgImage {
  position: relative;
}

.planTableBgImage::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/CardBgNoise.svg");
  background-color: #111111;
  background-size: 4%;
  opacity: 0.2;
  top: -1px;
  left: -1px;
  border-radius: 24px;
  z-index: -1;
}

@media (max-width: 767px) {
  .button {
    padding: 5px 15px;
    font-size: 13px;
  }

  .planBgImage::before {
    border-radius: 16px;
  }
}