import PlanDataGrid from "@/components/DataGrid";
import PageLayout from "@/components/Layout";
import {
  BiStar,
  Calendar,
  CICD,
  PiggyBank,
  TopRightArrow,
  Wallet,
} from "@/constants/icons";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import { getPlans } from "@/redux/slices/pricingSlice";
import { RootState, useAppDispatch } from "@/redux/store";
import { Box, Button, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import styles from "./index.module.css";
import { motion } from "framer-motion";
import HighlightWords from "../WordHighlight";
import { DASHBOARD_LINK } from "@/constants/vars";

function Plans() {
  const isMobile = useMediaQuery("(max-width: 767px)");

  const dispatch = useAppDispatch();

  const plans = useSelector((state: RootState) => state.pricingSlice.plans);

  const isTablet = useMediaQuery("(max-width: 1023px)");

  const router = useRouter();

  useEffect(() => {
    dispatch(
      getPlans({
        region: "Mumbai",
        type: "SERVER",
        subType:"web"
      })
    );
  }, []);

  return (
    <PageLayout
      className={`flex flex-col justify-center items-center font-manrope md:px-5 lg:px-0 w-full md:w-[80%] lg:w-[85%] z-50`}
    >
      <Box className="flex flex-col gap-3 items-center justify-center">
        <Typography className="text-br-secondary font-manrope font-medium flex items-center text-base md:text-lg gap-1 md:gap-2.5">
          <BiStar /> Flexible, Transparent Pricing for Every Developer & Business
        </Typography>
        <Typography
          className={`text-xl leading-relaxed font-manrope text-center font-semibold md:text-[28px] ${styles.emp_dev_text}`}
        >
          Choose a cost-effective, pay-as-you-go plan designed to scale with your needs.
        </Typography>
      </Box>
      <Box className="h-8 md:h-14" />
      <Box className="grid grid-cols-1 md:grid-cols-7 w-full gap-5 lg:gap-10 pl-0 md:pl-5 lg:pl-14 md:pt-5 justify-center items-center">
        <Box
          className={`grid col-span-7 md:col-span-2 ${styles.planBgImage} rounded`}
        >
          <Box
            className={`p-4 lg:px-6 lg:py-6 flex flex-col gap-5 rounded-2xl md:rounded-xl md:rounded-t-xl lg:rounded-t-2xl lg:rounded-b-none`}
            sx={{
              border: "1px dashed #303030",
              borderBottom: isMobile ? "1px dashed #303030" : "none",
              background:
                "linear-gradient(154.94deg, #1F1F1F 6.03%, #000000 84.72%)",
            }}
          >
            <Typography className="lg:text-xl font-semibold text-tx-primary gradient_text">
              No Strings Attached,
            </Typography>
            <Link
              href={DASHBOARD_LINK}
              passHref={true}
              target="_blank"
              className="w-full"
              title="Deploy for Free"
            >
              <Button
                sx={{ width: "100%" }}
                className={styles.button + " py-6 font-manrope font-medium"}
              >
                Deploy for Free
              </Button>
            </Link>
            <span className="border-dashed border-[1px] border-[#303030]"></span>
            <Typography className="lg:text-base text-[#9E9E9E] font-manrope">
              & scale as you need. No Nonsense pricing
            </Typography>
            {isMobile && (
              <Button
                className={
                  styles.view_details +
                  " gap-2 text-tx-primary font-manrope py-6"
                }
                sx={{
                  border: "2px solid #323232",
                }}
                onClick={() => {
                  router.push("/pricing");
                }}
              >
                View In Detail <TopRightArrow />
              </Button>
            )}
          </Box>
        </Box>
        <Box className="grid col-span-7 md:col-span-5 gap-5 justify-center items-center md:px-0">
          <Box className="flex flex-col md:flex-row justify-center items-center gap-5 md:gap-0">
            {/* col 1 */}
            <Box className="grid col-span-7 justify-center items-center gap-5 md:gap-10">
              {/* feat one */}
              <Box className="flex gap-5 justify-center items-center lg:max-w-[80%]">
                <Box
                  className="bg-bg-secondary p-2 md:p-2.5 rounded-lg md:rounded-xl"
                  sx={{ border: "1px dashed #303030" }}
                >
                  <Calendar
                    height={isTablet ? "20" : "26"}
                    width={isTablet ? "20" : "26"}
                  />
                </Box>
                <motion.div
                  initial={{ opacity: 0, x: isMobile ? 0 : -100 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  <HighlightWords
                    text="Complimentary Month-Long Trial: No Commitment"
                    wordsToHighlight={["Month-Long Trial"]}
                    highlightColor="#D4D4D4"
                    className="text-sm lg:text-base text-tx-tertiary leading-relaxed font-manrope font-medium"
                  />
                </motion.div>
              </Box>

              {/* feat two */}
              <Box className="flex gap-5 justify-start md:justify-center items-center lg:max-w-[70%]">
                <Box
                  className="bg-bg-secondary p-2 md:p-2.5 rounded-lg md:rounded-xl"
                  sx={{ border: "1px dashed #303030" }}
                >
                  <Wallet
                    height={isTablet ? "20" : "26"}
                    width={isTablet ? "20" : "26"}
                  />
                </Box>
                <motion.div
                  initial={{ opacity: 0, x: isMobile ? 0 : -100 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                  viewport={{ once: true }}
                >
                  <HighlightWords
                    text="No Team Plans, Just Pay for What You Use"
                    wordsToHighlight={["No Team Plans"]}
                    highlightColor="#D4D4D4"
                    className="text-sm lg:text-base text-tx-tertiary leading-relaxed font-manrope font-medium"
                  />
                </motion.div>
              </Box>
            </Box>

            {/* col 2 */}
            <Box className="grid col-span-7 justify-center items-start gap-5 md:gap-10">
              {/* feat three */}
              <Box className="flex gap-5 justify-center items-center lg :max-w-[70%]">
                <Box
                  className="bg-bg-secondary p-2 md:p-2.5 rounded-lg md:rounded-xl"
                  sx={{ border: "1px dashed #303030" }}
                >
                  <PiggyBank
                    height={isTablet ? "20" : "26"}
                    width={isTablet ? "20" : "26"}
                  />
                </Box>
                <motion.div
                  initial={{ opacity: 0, x: isMobile ? 0 : -100 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 1 }}
                  viewport={{ once: true }}
                >
                  <HighlightWords
                    text="Flexible Pay-As-You-Go Pricing: No Strings Attached"
                    wordsToHighlight={["Pay-As-You-Go"]}
                    highlightColor="#D4D4D4"
                    className="text-sm lg:text-base text-tx-tertiary leading-relaxed font-manrope font-medium"
                  />
                </motion.div>
              </Box>

              {/* feat four */}
              <Box className="flex gap-5 justify-start items-center lg :max-w-[55%]">
                <Box
                  className="bg-bg-secondary p-2 md:p-2.5 rounded-lg md:rounded-xl"
                  sx={{ border: "1px dashed #303030" }}
                >
                  <CICD
                    height={isTablet ? "20" : "26"}
                    width={isTablet ? "20" : "26"}
                  />
                </Box>
                <motion.div
                  initial={{ opacity: 0, x: isMobile ? 0 : -100 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 1.5 }}
                  viewport={{ once: true }}
                >
                  <HighlightWords
                    text="Unlimited CI/CD Integration Included"
                    wordsToHighlight={["Unlimited CI/CD"]}
                    highlightColor="#D4D4D4"
                    className="text-sm lg:text-base text-tx-tertiary leading-relaxed font-manrope font-medium"
                  />
                </motion.div>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
      {!isMobile && (
        <Box
          className={`h-full w-full p-1 ${styles.planTableBgImage}`}
          sx={{
            border: "1px dashed #303030",
            borderRadius: "24px",
            // background: "#161616",
          }}
        >
          <PlanDataGrid
            rows={plans["SERVER"].slice(0, 3)}
            isLastRowBlur={true}
            autoHeight={true}
            type="SERVER"
          />
          <Box className="flex w-full p-2 justify-center items-center z-50 shadow-2xl cursor-pointer text-[#DDDDDD] text-base hover:text-white duration-300 ease-in-out">
            <Typography
              className="flex justify-center items-center gap-2 py-2"
              onClick={() => {
                router.push("/pricing");
              }}
            >
              View in Details <TopRightArrow height={"18"} width={"18"} />
            </Typography>
          </Box>
        </Box>
      )}

      <Box className="h-20" />
    </PageLayout>
  );
}

export default Plans;
