import { Box, Typography } from "@mui/material";
import styles from "../../page.module.css";

import {
  CloudCube,
  Database,
  Globe,
  InfiniteCiCd,
  Live,
  LoadBalancer,
  PersonSupport,
  RealTimeMonitoring,
  StarIcon,
  VerticalScaling,
} from "@/constants/icons";
import { BG_NOISE_IMAGE_URL, INSTANT_DEPLOYMENT_URL } from "@/constants/vars";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import { motion } from "framer-motion";
import { Manrope } from "next/font/google";

const manrope = Manrope({ subsets: ["latin"] });

function Features() {
  const isMobile = useMediaQuery("(max-width: 767px)");

  const bgNoiseImageUrl = BG_NOISE_IMAGE_URL;

  const instantDeploymentUrl = INSTANT_DEPLOYMENT_URL;

  return (
    <>
      <Box className="flex flex-col gap-3 items-center justify-center">
        <Typography className="text-br-secondary font-manrope font-medium flex items-center text-lg md:text-lg gap-2.5">
          <StarIcon /> Powerful Features
        </Typography>
        <Typography
          className={`text-xl leading-relaxed font-manrope text-center font-bold md:text-[32px] ${styles.emp_dev_text} heading_text`}
        >
          Built to Save You Time & Money
        </Typography>
      </Box>

      <Box className="h-10 md:h-14" />

      <Box className="grid grid-cols-1 lg:grid-cols-3 justify-stretch w-full md:w-[80%] lg:w-[85%] h-full gap-y-5 lg:gap-5 px-3.5 font-manrope">
        <Box className="col-span-1 grid grid-cols-1 lg:grid-rows-[1fr 2fr 1fr] gap-5">
          <motion.div
            initial={{ opacity: 0, x: isMobile ? 0 : -100 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Box
              className="grid grid-cols-1 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl"
              sx={{
                background: bgNoiseImageUrl,
              }}
            >
              <Box className="w-full flex flex-col p-6 gap-2.5">
                <Typography className="text-lg font-medium font-manrope text-tx-primary">
                  Instant Deployments
                </Typography>
                <Typography className="text-[15px] font-medium font-manrope leading-relaxed text-tx-secondary">
                  Reach your customers faster than ever before by eliminating
                  bottlenecks and manual errors. Take your idea from concept to
                  reality in the blink of an eye.
                </Typography>
              </Box>

              <Box
                sx={{
                  backgroundImage: instantDeploymentUrl,
                  height: "130px",
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "100% 95%",
                  backgroundSize: "auto",
                  borderBottomLeftRadius: "12px",
                  borderBottomRightRadius: "12px",
                  position: "relative",
                  zIndex: 1,
                }}
              >
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,

                    width: "100%",
                    height: "40%",
                    background:
                      "linear-gradient(to bottom, #171717 0%, rgba(255, 255, 255, 0) 100%)",
                  }}
                />
              </Box>
            </Box>
          </motion.div>
          {/* Second child, now with increased space allocation */}
          <Box className="grid grid-cols-1 md:grid-cols-2 gap-5 overflow-hidden">
            {/* Effortless Environment Management */}
            <motion.div
              initial={{ opacity: 0, x: isMobile ? 0 : -100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="p-5 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl gap-2.5 flex flex-col justify-between h-full"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                <Typography className="text-base font-medium text-tx-primary font-manrope">
                  Effortless Environment Management
                </Typography>
                <Typography className="text-[15px] font-manrope font-medium text-tx-secondary">
                  Customize configurations, manage and monitor resources across
                  environments
                </Typography>
              </Box>
            </motion.div>

            {/* Personalized Domain Integration */}
            <motion.div
              initial={{ opacity: 0, x: isMobile ? 0 : -200 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="pl-5 py-5 gap-3 md:py-5 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl flex flex-col md:justify-between h-full"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                <Box className="pr-5">
                  <Typography className="text-base font-medium text-tx-primary font-manrope">
                    Personalized Domain Integration
                  </Typography>
                </Box>

                <Box className="px-3 py-1 border border-[#2d2d2d] border-r-0 flex gap-1.5 bg-[#191818] rounded-l-xl items-center">
                  <Box>
                    <Globe />
                  </Box>
                  <Box className="flex m-2 px-2 py-1.5 w-full rounded-md bg-[#343434]">
                    <Typography className="text-xs font-medium text-[#a9a9a9] font-manrope text-nowrap">
                      https:// www.creativeshots.com
                    </Typography>
                  </Box>
                </Box>

                <Box className="">
                  <Typography className="text-[15px] font-manrope font-medium text-tx-secondary">
                    Empower your team to build, test, and deploy code
                  </Typography>
                </Box>
              </Box>
            </motion.div>
          </Box>

          {/* Third child, now with reduced height */}
          <Box className="grid grid-cols-3 gap-5">
            <motion.div
              initial={{ opacity: 0, x: isMobile ? 0 : -100 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="p-3 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl h-full flex flex-col gap-2"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                <Live />
                <Typography
                  className={
                    "text-sm font-medium text-tx-primary font-manrope pt-3 " +
                    manrope.className
                  }
                >
                  Live log monitoring
                </Typography>
              </Box>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: isMobile ? 0 : -200 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="p-3 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl h-full flex flex-col gap-2"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                <CloudCube />
                <Typography
                  className={
                    "text-sm font-medium text-tx-primary font-manrope pt-3 " +
                    manrope.className
                  }
                >
                  99% Uptime
                </Typography>
              </Box>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: isMobile ? 0 : -300 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="p-3 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl h-full flex flex-col gap-2"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                <PersonSupport />
                <Typography
                  className={
                    "text-sm font-medium text-tx-primary font-manrope pt-3 " +
                    manrope.className
                  }
                >
                  Dedicated Support
                </Typography>
              </Box>
            </motion.div>
          </Box>
        </Box>

        {/* Right column (2/3 width) */}
        <Box className="col-span-2 w-full grid grid-cols-1 lg:grid-rows-2 gap-5">
          {/* First row */}
          <Box className="flex flex-col lg:flex-row gap-5">
            {/* Infinite CI/CD */}
            <motion.div
              initial={{ opacity: 0, y: isMobile ? 0 : 100 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="p-6 gap-3 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl flex flex-1 flex-col md:justify-between h-full"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                <Typography className="text-lg font-medium text-tx-primary font-manrope">
                  AI Powered Deployments
                </Typography>
                <Box className="flex w-full px-8 justify-center">
                  <InfiniteCiCd />
                </Box>
                <Typography className="text-[15px] font-manrope font-medium leading-relaxed text-tx-secondary">
                  Empower your team to build, test, and deploy code without
                  restrictions on minutes or deployments.
                </Typography>
              </Box>
            </motion.div>

            {/* flexible scaling solutions */}
            <motion.div
              initial={{ opacity: 0, y: isMobile ? 0 : 100 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl pl-6 py-6 gap-7 grid grid-rows-[2fr 1fr] h-full"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                {/* illustration */}
                <Box className="bg-[#222222] py-4 pl-5 rounded-l-2xl border-2 border-stone-800 border-r-0 flex flex-col gap-4 overflow-hidden">
                  <Box className="flex gap-4 justify-start items-center">
                    <Typography className="text-base text-archivo text-tx-primary font-medium">
                      Current Plan
                    </Typography>
                    <Box className="bg-[#203721] text-sm font-archivo font-medium text-[#4fa14f] py-1 px-2.5 rounded-lg">
                      Starter
                    </Box>
                  </Box>
                  <Box className="flex gap-5 items-center justify-start">
                    <VerticalScaling />
                    <Box className="flex w-full flex-col gap-0.5 justify-start overflow-hidden">
                      <Typography className="text-[15px] text-manrope text-stone-400 font-medium">
                        Vertical Scaling
                      </Typography>
                      <Typography className="text-sm text-nowrap text-manrope text-tx-secondary ">
                        Increase the memory of the existing machine
                      </Typography>
                    </Box>
                  </Box>
                  <Box className="flex items-center py-2 px-3 gap-2 bg-br-accent text-br-secondary rounded-l-lg shadow-sm">
                    <Database />
                    <Typography className="text-sm font-manrope font-medium">
                      Upgrade Plan
                    </Typography>
                  </Box>
                </Box>

                {/* Title and description */}
                <Box className="flex flex-col gap-2">
                  <Typography className="text-lg font-medium text-tx-primary font-manrope">
                    Flexible Scaling Solutions
                  </Typography>
                  <Typography className="text-[15px] w-[90%] font-manrope font-medium text-tx-secondary">
                    Plan long-term growth with Kuberns effortless horizontal or
                    vertical scaling capabilities
                  </Typography>
                </Box>
              </Box>
            </motion.div>
          </Box>

          {/* Second row */}
          <Box className="grid grid-cols-1 lg:grid-cols-2 gap-5">
            {/* cols 1 - real time monitoring */}
            <motion.div
              initial={{ opacity: 0, y: isMobile ? 0 : -100 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <Box
                className="pl-6 pt-6 gap-5 bg-bg-secondary border-2 border-st-light border-dashed rounded-xl shadow-xl flex flex-col md:justify-between h-full"
                sx={{
                  background: bgNoiseImageUrl,
                }}
              >
                <Box className="flex flex-col gap-2.5 pr-6">
                  <Typography className="text-lg font-medium text-tx-primary font-manrope">
                    Real-time Monitoring & Alerts
                  </Typography>
                  <Typography className="text-[15px] font-manrope font-medium leading-relaxed text-tx-secondary">
                    Gain real time visibility to application's behavior through
                    metrics and notification with smart alerting
                  </Typography>
                </Box>
                <Box className="bg-[#222222] border-2 border-st-light border-dashed pl-6 pt-6 rounded-tl-xl rounded-br-xl overflow-hidden">
                  <RealTimeMonitoring />
                </Box>
              </Box>
            </motion.div>

            {/* cols 2 */}
            <Box className="grid grid-rows-1 gap-5">
              <motion.div
                initial={{ opacity: 0, x: isMobile ? 0 : 100 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Box
                  className="p-6 border-2 border-st-light border-dashed bg-bg-secondary rounded-xl shadow-xl flex flex-col gap-2.5 h-full"
                  sx={{
                    background: bgNoiseImageUrl,
                  }}
                >
                  <Typography className="text-lg font-medium text-tx-primary font-manrope">
                    Environment Variable Configuration
                  </Typography>
                  <Typography className="text-[15px] font-manrope font-medium leading-relaxed text-tx-secondary">
                    Customize configurations, manage and monitor resources
                    across environments
                  </Typography>
                </Box>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: isMobile ? 0 : 100 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <Box
                  className="p-6 flex flex-row flex-1 gap-6 bg-bg-secondary md:items-center justify-center  border-2 border-st-light border-dashed rounded-xl shadow-xl h-full"
                  sx={{
                    background: bgNoiseImageUrl,
                  }}
                >
                  <Box className="bg-[#222222] rounded-lg md:rounded-2xl flex justify-center items-center p-3 md:p-6 h-16 w-20 md:h-20 md:w-20">
                    <LoadBalancer />
                  </Box>
                  <Box className="flex flex-col gap-2">
                    <Typography className="text-lg font-medium text-tx-primary font-manrope ">
                      Load Balancing
                    </Typography>
                    <Typography className="text-[15px] font-medium font-manrope leading-relaxed text-tx-secondary">
                      Plan long-term growth with Kuberns effortless horizontal
                      or vertical scaling capabilities
                    </Typography>
                  </Box>
                </Box>
              </motion.div>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
}

export default Features;
