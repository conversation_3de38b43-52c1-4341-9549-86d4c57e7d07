.videoBgImage {
  position: relative;
}

.videoBgImage::before {
  content: "";
  position: absolute;
  width: 1327px;
  height: 303px;
  flex-shrink: 0;
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/introvideo.shadow.svg");
  background: linear-gradient(180deg, #000 0%, #484848 100%);
  filter: blur(125px);
  background-size: contain;
  background-position: top;
  top: 0;
  z-index: 15;
}

.active_step {
  position: relative;
  z-index: 0;
  height: 45px;
  width: 210px;
  overflow: hidden;
  cursor: pointer;
  border: none !important;
  padding: 0 !important;
  background: #0e0e0e !important;
  color: #656565;
  border-radius: 25px;
  min-width: fit-content;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 50px 0 rgb(0 0 0 / 7%);
  transition: 0.2s all linear;
  text-decoration: initial;
}

.active_step span {
  position: relative;
  z-index: 1;
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  top: 2px;
  left: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 20.25px;
  /* border: 1px dashed #2b2b2b; */
  background: radial-gradient(50% 50% at 50% 50%, #222 0%, #111 100%);
  backdrop-filter: blur(6.074999809265137px);
}

.active_step:after {
  content: "";
  position: absolute;
  z-index: -2;
  left: -50%;
  top: -50%;
  width: 200%;
  height: 200%;
  background-color: transparent;
  background-repeat: no-repeat;
  background-size: 50% 50%, 50% 50%;
  background-position: 0 0, 100% 0, 100% 100%, 0 100%;
  background-image: linear-gradient(90deg,
      rgba(255, 255, 255, 0) 0%,
      #3c6ce4 50%,
      rgba(255, 255, 255, 0) 100%);
  -webkit-animation: rotate 3s linear infinite;
  animation: rotate 3s linear infinite;
}

.inActive_step {
  position: relative;
  z-index: 0;
  height: 45px;
  width: 210px;
  overflow: hidden;
  cursor: pointer;
  border: none !important;
  padding: 0 !important;
  color: #656565;
  border-radius: 25px;
  min-width: fit-content;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 4px 50px 0 rgb(0 0 0 / 7%);
  transition: 0.2s all linear;
  text-decoration: initial;
}

.inActive_step span {
  position: relative;
  z-index: 1;
  height: calc(100% - 4px);
  width: calc(100% - 4px);
  top: 2px;
  left: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #cacaca;
  border-radius: 20.25px;
  border: 1px dashed #2b2b2b;
  background: radial-gradient(50% 50% at 50% 50%, #222 0%, #111 100%);
  backdrop-filter: blur(6.074999809265137px);
}

.intro_vdo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  aspect-ratio: 16/9;
  border: 0px;
  border-radius: 20px 20px 0px 0px;
}

.button {
  padding: 10px 30px;
  border: none;
  color: white;
  cursor: pointer;
  outline: none;
  font-size: 15px;
  border-radius: 25px;
  background-image: linear-gradient(to right, #2758d1 15%, #172285 45%);
  background-size: 200% 100%;
  transition: background-position 1s;
  z-index: 1000;
  background-position: 0 0;
}

.button:hover {
  background-position: 100% 250%;
}

.play_again_button {
  padding: 10px 30px;
  border: none;
  cursor: pointer;
  color: white;
  outline: none;
  font-size: 15px;
  border-radius: 25px;
  background: #808080;
  display: flex;
  justify-content: center;
  align-items: center;
}

.play_again_button:hover {
  background: #606060;
}

@media (max-width: 767px) {
  .button {
    padding: 5px 15px;
    font-size: 13px;
  }

  .inActive_step {
    width: 150px;
  }

  .intro_vdo {
    border-radius: 10px 10px 0px 0px;
  }

  .inActive_step {
    width: 45px !important;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}