"use client";

import { Application, Checked, Code, Github, Live } from "@/constants/icons";
import { INTRO_VIDEO } from "@/constants/vars";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import { isBrowser } from "@/utils";
import ReplayIcon from "@mui/icons-material/Replay";
import { Box, Button, Typography } from "@mui/material";
import { LegacyRef, useEffect, useRef, useState } from "react";
import { Element, scroller } from "react-scroll";
import styles from "./index.module.css";

interface StepStatus {
  [key: number]: "pending" | "active" | "completed";
}

function Video() {
  if (!isBrowser()) return;

  const videoRef: any = useRef(null);

  const divRef: LegacyRef<HTMLDivElement> = useRef(null);

  const hasRun = useRef(false);

  const [isVideoEnded, setIsVideoEnded] = useState(false);

  const [isIntersecting, setIsIntersecting] = useState(false);

  const [stepStatus, setStepStatus] = useState<StepStatus>({
    1: "pending",
    2: "pending",
    3: "pending",
    4: "pending",
  });

  const isMobile = useMediaQuery("(max-width: 767px)");

  const handleStepStatus = (statusObj: StepStatus) => {
    setStepStatus((prev) => {
      return { ...prev, ...statusObj };
    });
  };

  const handlePlayAgain = () => {
    hasRun.current = false;
    videoRef.current.currentTime = 2;
    videoRef?.current?.play();
    handleStepStatus({
      1: "active",
      2: "pending",
      3: "pending",
      4: "pending",
    });
    setIsVideoEnded(false);
  };

  useEffect(() => {
    if (divRef && divRef.current) {
      divRef.current.click();
    }

    const handleScroll = () => {
      if (window.scrollY > 100) {
        window.removeEventListener("scroll", handleScroll);
      } else {
        scroller.scrollTo("myImage", {
          duration: 1000,
          delay: 0,
          smooth: true,
          offset: -100,
        });

        handlePlayAgain();
        setIsIntersecting(true);

        window.removeEventListener("scroll", handleScroll);
      }
    };

    const handleIntersection = (entries: any, observer: any) => {
      entries.forEach((entry: any) => {
        if (entry.isIntersecting) {
          handlePlayAgain();
        } else {
          handleStepStatus({
            1: "pending",
            2: "pending",
            3: "pending",
            4: "pending",
          });
          videoRef?.current?.pause();
          setIsIntersecting(false);
        }
      });
    };

    const options = {
      root: null,
      rootMargin: "0px",
      threshold: 0.6,
    };

    const observer = new IntersectionObserver(handleIntersection, options);

    if (videoRef.current) observer.observe(videoRef.current);

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (videoRef.current) observer.unobserve(videoRef.current);
    };
  }, []);

  const delay = (ms: number) =>
    new Promise((resolve, reject) => {
      let elapsed = 0;
      const interval = setInterval(() => {
        if (videoRef && videoRef?.current?.paused) {
          clearInterval(interval);
          handleStepStatus({
            1: "pending",
            2: "pending",
            3: "pending",
            4: "pending",
          });
          reject(false);
        } else {
          elapsed += 1000;
          if (elapsed >= ms) {
            clearInterval(interval);
            resolve(true);
          }
        }
      }, 1000);
    });

  useEffect(() => {
    const updateStepStatus = async () => {
      if (
        stepStatus[1] !== "pending" &&
        stepStatus[4] !== "completed" &&
        !hasRun.current
      ) {
        hasRun.current = true; // Ensure this block only runs once
        await delay(7500);
        handleStepStatus({ 1: "completed", 2: "active" });

        await delay(5000);
        handleStepStatus({ 2: "completed", 3: "active" });

        await delay(18500);
        handleStepStatus({ 3: "completed", 4: "active" });

        await delay(4000);
        setIsVideoEnded(true);
        handleStepStatus({ 4: "completed" });
      }
    };

    updateStepStatus();
  }, [stepStatus]);

  useEffect(() => {
    if (isIntersecting && videoRef?.current?.paused) {
      videoRef.current.play();
    }
  }, [isIntersecting]);

  const steps = [
    {
      id: 1,
      title: "Code your Idea",
      icon: (
        <Code height={isMobile ? "15" : "19"} width={isMobile ? "15" : "19"} />
      ),
    },
    {
      id: 2,
      title: "Get it from Github",
      icon: (
        <Github
          height={isMobile ? "15" : "19"}
          width={isMobile ? "15" : "19"}
        />
      ),
    },
    {
      id: 3,
      title: "Configure your Needs",
      icon: (
        <Application
          height={isMobile ? "15" : "19"}
          width={isMobile ? "15" : "19"}
        />
      ),
    },
    {
      id: 4,
      title: "See the magic happen!",
      icon: (
        <Live
          fill="#FFF"
          height={isMobile ? "15" : "20"}
          width={isMobile ? "15" : "20"}
        />
      ),
    },
  ];

  const getStyle = (id: number) => {
    if (stepStatus[id] === "active") return styles.active_step;
    else {
      if (isMobile) return styles.inActive_step + " w-10";
      else return styles.inActive_step;
    }
  };

  return (
    <Element
      name="myImage"
      className={`flex justify-center h-full w-full z-[999] -mt-[8%]`}
    >
      <div ref={divRef} />
      <Box
        className={`flex flex-col justify-center items-center overflow-hidden w-full md:w-[80%] lg:w-[85%] h-full rounded-2xl bg-[#141414] md:rounded-[32px] relative ${styles.videoBgImage} noiseBgImage px-1 md:px-8`}
        sx={{
          border: "1px dashed #232323",
        }}
      >
        {/* md:pt-6 */}
        <Box className="flex w-full items-center justify-between pt-2 md:pt-2 z-10">
          <p className="text-nowrap p-2 md:p-4 hidden md:block text-lg">
            Deploy for <span className="gradient_text">free</span>
          </p>
          <Box
            className={`flex justify-center md:justify-end items-center w-full gap-1 md:gap-5 duration-300 p-2 md:p-4 ease-in`}
          >
            {steps.map((step) => {
              return (
                <div
                  className={`${getStyle(step.id)} bg-inherit`}
                  key={step.id}
                >
                  <span>
                    {stepStatus[step.id] === "completed" ? (
                      <Checked
                        height={isMobile ? "15" : "19"}
                        width={isMobile ? "15" : "19"}
                      />
                    ) : (
                      step.icon
                    )}
                    {isMobile && stepStatus[step.id] === "active" && (
                      <Typography className="text-[13px] pl-2">
                        {step.title}
                      </Typography>
                    )}
                    {!isMobile && (
                      <Typography className="text-[8px] md:text-[13px] pl-2">
                        {step.title}
                      </Typography>
                    )}
                  </span>
                </div>
              );
            })}
          </Box>
        </Box>
        <Box className="w-full pb-1 md:pb-8 pt-2 md:pt-4 z-20">
          <video
            ref={videoRef}
            className={`${styles.intro_vdo} duration-300 object-fill rounded-xl md:rounded-3xl ease-in bg-transparent`}
            muted
          >
            <source src={INTRO_VIDEO} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </Box>
        {isVideoEnded && (
          <Box className="grid grid-cols-1 h-full -mt-5 gap-3 absolute bottom-0 w-full px-0 md:px-5 z-50">
            <Box className="z-50 flex gap-5 w-full h-fit justify-center items-center bg-black bg-opacity-85 text-white text-xl shadow-xl rounded-xl md:rounded-3xl cursor-pointer">
              <Button
                variant="contained"
                size={"medium"}
                className={styles.button}
              >
                Deploy for Free
              </Button>
              <Button
                variant="contained"
                size={"medium"}
                className={styles.play_again_button}
                onClick={handlePlayAgain}
              >
                <ReplayIcon fontSize="small" />
                Play Again
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    </Element>
  );
}

export default Video;
