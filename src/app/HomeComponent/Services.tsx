import { Box, Typography } from "@mui/material";
import React from "react";
import styles from "../page.module.css";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import MobileTimeLine from "@/components/ServicesTimeLine/MobileTimeLine";
import ServiceTimeLine from "@/components/ServicesTimeLine/TimeLine";
import { BiStar } from "@/constants/icons";

function Services() {
  const isMobile = useMediaQuery("(max-width: 767px)");

  return (
    <>
      <Box className="flex flex-col gap-3 items-center justify-center">
        <Typography className="text-br-secondary font-manrope font-medium flex items-center text-lg md:text-lg gap-1.5 md:gap-2.5">
          <BiStar /> Everything You Need, Right Here!
        </Typography>
        <Typography
          className={`text-xl leading-relaxed font-manrope text-center font-bold md:text-[32px] ${styles.emp_dev_text} heading_text`}
        >
          Simplify, Automate, and Scale Your Cloud Deployments in minutes
        </Typography>
      </Box>
      <Box className="">
        {isMobile ? (
          <>
            <Box className="h-[150px]" />
            <MobileTimeLine />
          </>
        ) : (
          <>
            <ServiceTimeLine />
            <Box className="min-h-[3300px]" />
          </>
        )}
      </Box>
    </>
  );
}

export default Services;
