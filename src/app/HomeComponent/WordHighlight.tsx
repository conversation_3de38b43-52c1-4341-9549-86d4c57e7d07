import { Typography } from "@mui/material";
import React from "react";

interface Props {
  text: string;
  wordsToHighlight: string[];
  highlightColor?: string;
  className?: string;
  highlightClassName?: string;
}

const HighlightWords = ({
  text,
  wordsToHighlight,
  highlightColor = "yellow",
  className,
  highlightClassName,
}: Props) => {
  const regex = new RegExp(`\\b(${wordsToHighlight.join("|")})\\b`, "gi");
  const parts = text.split(regex);

  return (
    <Typography component="span" className={className}>
      {parts.map((part, index) =>
        regex.test(part) ? (
          <span
            key={index}
            style={{ color: highlightColor }}
            className={`${highlightClassName} leading-relaxed`}
          >
            {part}
          </span>
        ) : (
          <span key={index}>{part}</span>
        )
      )}
    </Typography>
  );
};

export default HighlightWords;
