import { AWS, BEHANCE, CD, GITHUB, LADSPA, LINKEDIN } from "@/constants/vars";
import { Box, Typography } from "@mui/material";
import { motion } from "framer-motion";
import Image from "next/image";
import styles from "./index.module.css";

function Companies() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
      className="flex flex-col justify-center items-center w-full md:w-[80%] lg:w-[85%] z-50"
    >
      <Typography className="heading_text text-center font-manrope font-medium leading-relaxed text-lg md:text-2xl">
        Trusted by the Best
      </Typography>
      <Box className="h-5" />
      <Typography className="text-[#9E9E9E] text-center font-manrope font-medium leading-relaxed text-base md:text-lg md:max-w-[400px]">
        Empowering businesses to deploy faster, save costs, and scale
        seamlessly.
      </Typography>
      <Box className="h-6 md:h-16" />
      <Box
        className={`w-full ${styles.companyBgImage} flex items-center justify-between overflow-x-auto gap-10`}
      >
        {CompanyLogos.map((item, index) => (
          <span key={index} className="flex-shrink-0 space-x-4">
            {item.image}
          </span>
        ))}
      </Box>
    </motion.div>
  );
}

export default Companies;

const CompanyLogos = [
  {
    id: 1,
    image: <Image src={AWS} alt="aws" width={120} height={120} title="Deploy your backend in one click with Kuberns" />,
  },
  {
    id: 2,
    image: <Image src={CD} alt="aws" width={120} height={120} title="Deploy your backend in one click with Kuberns" />,
  },
  {
    id: 3,
    image: <Image src={BEHANCE} alt="aws" width={120} height={120} title="Deploy your backend in one click with Kuberns" />,
  },
  {
    id: 4,
    image: <Image src={LINKEDIN} alt="aws" width={120} height={120} title="Deploy your backend in one click with Kuberns" />,
  },
  {
    id: 5,
    image: <Image src={GITHUB} alt="aws" width={80} height={80} title="Deploy your backend in one click with Kuberns" />,
  },
  {
    id: 6,
    image: <Image src={LADSPA} alt="aws" width={120} height={120} title="Deploy your backend in one click with Kuberns" />,
  },
];
