import { BiStar } from "@/constants/icons";
import { WORLD_MAP } from "@/constants/vars";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import { Box, Typography } from "@mui/material";
import { motion } from "framer-motion";
import Image from "next/image";
import styles from "../page.module.css";

function ServiceSpanGlobe() {
  const isMobile = useMediaQuery("(max-width: 767px)");

  return (
    <>
      {!isMobile && (
        <>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="flex flex-col justify-center items-center"
          >
            <Box className="h-10 md:h-40" />
            <Box className="flex flex-col gap-3 items-center justify-center">
              <Typography className="text-br-secondary font-manrope font-medium flex items-center text-lg md:text-lg gap-2.5">
                <BiStar /> Deploy Anywhere, Scale Everywhere
              </Typography>
              <Typography
                className={`text-xl leading-relaxed text-center font-manrope font-semibold md:text-[28px] ${styles.emp_dev_text}`}
              >
                Go global with one click—deploy instantly, anywhere.
              </Typography>
            </Box>
            <Box className="h-10 md:h-16" />
            <Image
              src={WORLD_MAP}
              alt="world_map"
              width={2500}
              height={2500}
              className={styles.world_map}
              title="Deploy your backend in one click with Kuberns"
            />
          </motion.div>
        </>
      )}
    </>
  );
}

export default ServiceSpanGlobe;
