import { WITH_US } from "@/constants/vars";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import { Box } from "@mui/material";
import { motion } from "framer-motion";
import Image from "next/image";

function WithUs() {
  const isMobile = useMediaQuery("(max-width: 767px)");

  return (
    <motion.div
      initial={{ opacity: 0, x: isMobile ? 0 : 100 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
      className="relative group"
    >
      <Box className="absolute top-3 right-3 md:top-6 md:right-6 bg-[#161616] rounded-full border border-[#2E2E2E] px-4 md:px-6 py-1.5 md:py-2 cursor-default text-sm md:text-base font-medium font-manrope">
        With Us
      </Box>
      <Image
        src={WITH_US}
        height={isMobile ? "330" : "400"}
        width={isMobile ? "400" : "550"}
        alt="WithUs"
        title="Deploy your backend in one click with <PERSON>berns"
        className="rounded-3xl border border-dashed border-[#232323]  group-hover:shadow-lg group-hover:shadow-[#484848] transition-all duration-500 ease-in-out"
      />
    </motion.div>
  );
}

export default WithUs;
