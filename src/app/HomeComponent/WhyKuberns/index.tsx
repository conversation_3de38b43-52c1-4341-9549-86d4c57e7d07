import {
  BG_NOISE_IMAGE_URL,
  CHECKBOX_BOTTOM_LEFT_IMAGE_URL,
  CHECKBOX_TOP_IMAGE_URL,
  CHECKBOX_TOP_RIGHT_IMAGE_URL,
  LEAF_BOTTOM_RIGHT_IMAGE_URL,
  LEAF_TOP_IMAGE_URL,
  LEAF_TOP_LEFT_IMAGE_URL
} from "@/constants/vars";
import { Box, Button, Typography } from "@mui/material";
import { motion } from "framer-motion";
import Link from "next/link";
import styles from "../../page.module.css";
import WithOutUs from "./WithOutUs";
import WithUs from "./WithUs";

function WhyKuberns() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
      className="flex flex-col justify-center items-center w-full md:w-[80%] lg:w-[85%]"
    >
      <Link href={"/about"} passHref={false} title="Why Kuberns">
        <Button
          variant="contained"
          size={"large"}
          className={
            styles.why_kuberns_btn +
            " font-manrope font-semibold text-tx-primary "
          }
        >
          Why KUBERNS?
        </Button>
      </Link>
      <Box className="h-5" />
      <Typography className="text-[#9E9E9E] text-center font-manrope font-medium leading-relaxed text-base md:text-lg md:max-w-[702px]">
        "All In Platform as a Service (PaaS) that simplifies deployments with AI powered cloud Autopilot—allowing you to focus on building great
        products while we handle infrastructure, scaling, and deployments seamlessly."
      </Typography>
      <Box className="h-6 md:h-16" />
      <Box className="flex flex-col lg:flex-row w-full justify-between items-center gap-5 ">
        <WithOutUs />
        <WithUs />
      </Box>

      <Box className="h-10 md:h-36" />

      <Box className="flex flex-col gap-3 items-center justify-center">
        <Typography
          className="text-br-secondary font-bold flex items-center text-lg text-center md:text-2xl gap-1.5 md:gap-2.5 gradient_text tracking-wide"
          sx={{
            fontFamily: "DM Serif Text",
          }}
        >
          Saving Time and Money Isn’t Just a Promise - It’s a Guarantee.
        </Typography>
        <Typography
          className={`text-xl leading-relaxed font-manrope text-center font-semibold md:text-base text-tx-secondary`}
        >
          and Here’s How We Do It.
        </Typography>
      </Box>
      <Box className="h-10 md:h-16" />

      <Box className="flex flex-col-reverse gap-4 lg:gap-8 lg:flex-row w-full justify-center items-stretch">
        <Box className="w-full flex flex-col gap-2 items-center justify-center relative overflow-hidden rounded-3xl md:rounded-[36px] border border-dashed border-[#232323]">
          <Box
            className="absolute  w-full h-full"
            sx={{
              background: BG_NOISE_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPositionY: "top",
              backgroundPositionX: "center",
            }}
          />
          <Box
            className="absolute  w-full h-full"
            sx={{
              background: LEAF_TOP_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPositionY: "top",
              backgroundPositionX: "center",
            }}
          />
          <Box
            className="absolute w-full h-full"
            sx={{
              background: LEAF_TOP_LEFT_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundPositionY: "top",
              backgroundPositionX: "left",
            }}
          />
          <Box
            className="absolute  w-full h-full"
            sx={{
              background: LEAF_BOTTOM_RIGHT_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundPositionY: "bottom",
              backgroundPositionX: "right",
            }}
          />
          <Box className="py-[60px] px-9 flex flex-col items-center justify-center">
            <Typography
              className="font-manrope tracking-wide text-5xl md:text-[64px] leading-[70px] md:leading-[86.4px]"
              sx={{
                background:
                  "conic-gradient(from 180deg at 48.5% 50%, #FBFBFD 26.24999910593033deg, #C8D4DA 88.12500178813934deg, #FFF 156.58468008041382deg, #AEC0CE 191.74442768096924deg, #E3E9EE 237.1290135383606deg, #FAFBFC 255.19062280654907deg, #D6DFE6 310.1085305213928deg, #B8C9D3 331.875deg)",
                backgroundClip: "text",
                color: "transparent",
              }}
            >
              95%
            </Typography>
            <Typography className="font-manrope tracking-normal text-lg md:text-xl text-tx-primary leading-7 md:leading-[35px] italic">
              Efforts Reduced
            </Typography>
            <Typography className="font-manrope tracking-wide mt-2 w-3/4 text-sm md:text-base text-tx-primary text-center leading-7">
              Automate manual cloud tasks and focus on building better
              applications.
            </Typography>
          </Box>
        </Box>

        <Box className="w-full flex flex-col gap-2 items-center justify-center relative overflow-hidden rounded-3xl md:rounded-[36px] border border-dashed border-[#232323]">
          <Box
            className="absolute  w-full h-full"
            sx={{
              background: BG_NOISE_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPositionY: "top",
              backgroundPositionX: "center",
            }}
          />
          <Box
            className="absolute  w-full h-full"
            sx={{
              background: CHECKBOX_TOP_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundSize: "cover",
              backgroundPositionY: "top",
              backgroundPositionX: "center",
            }}
          />
          <Box
            className="absolute w-full h-full"
            sx={{
              background: CHECKBOX_TOP_RIGHT_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundPositionY: "top",
              backgroundPositionX: "right",
            }}
          />
          <Box
            className="absolute  w-full h-full"
            sx={{
              background: CHECKBOX_BOTTOM_LEFT_IMAGE_URL,
              backgroundRepeat: "no-repeat",
              backgroundPositionY: "bottom",
              backgroundPositionX: "left",
            }}
          />
          <Box className="py-[60px] px-9 flex flex-col items-center justify-center">
            <Typography
              className="font-manrope tracking-wide text-5xl md:text-[64px] leading-[70px] md:leading-[86.4px]"
              sx={{
                background:
                  "conic-gradient(from 180deg at 48.5% 50%, #FBFBFD 26.24999910593033deg, #C8D4DA 88.12500178813934deg, #FFF 156.58468008041382deg, #AEC0CE 191.74442768096924deg, #E3E9EE 237.1290135383606deg, #FAFBFC 255.19062280654907deg, #D6DFE6 310.1085305213928deg, #B8C9D3 331.875deg)",
                backgroundClip: "text",
                color: "transparent",
              }}
            >
              90%
            </Typography>
            <Typography className="font-manrope tracking-normal text-lg md:text-xl text-tx-primary leading-7 md:leading-[35px] italic">
              Cost Reduction
            </Typography>
            <Typography className="font-manrope tracking-wide mt-2 w-3/4 text-sm md:text-base text-tx-primary text-center leading-7">
              Lower your DevOps costs with Kuberns cloud Autopilot.
            </Typography>
          </Box>
        </Box>
      </Box>
    </motion.div>
  );
}

export default WhyKuberns;
