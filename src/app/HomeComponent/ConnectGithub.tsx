import { Box, Button, Typography } from "@mui/material";
import React from "react";
import styles from "../page.module.css";
import { Github } from "@/constants/icons";
import Link from "next/link";
import { motion } from "framer-motion";
import { DASHBOARD_LINK } from "@/constants/vars";

function ConnectGithub() {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8, delay: 0.5 }}
      viewport={{ once: true }}
      className="flex justify-center items-center w-full"
    >
      <Box
        className={`flex flex-col gap-5 w-full md:w-[80%] lg:w-[85%] border-2 h-[50vh] ${styles.connect_github} justify-center items-center`}
      >
        <Box className="w-10/12 md:w-2/5 flex justify-center text-center">
          <Typography className="font-manrope font-medium text-tx-primary leading-relaxed text-xl md:text-2xl">
            Try AI-powered Cloud Deployment for one month - Absolutely for free!
          </Typography>
        </Box>
        <span
          className="w-11/12 md:w-2/5"
          style={{
            border: "1px solid #313131",
          }}
        />
        <Box className="flex flex-col md:flex-row gap-3 md:gap-4 items-center justify-center md:pt-3">
          <Link
            href={DASHBOARD_LINK + "/user/general"}
            passHref={true}
            target="_blank"
            title="Connect with Github"
          >
            <Button
              className={
                "gap-3 py-5.5 px-5 font-manrope font-medium text-base md:text-base " +
                styles.connect_github_button
              }
              sx={{
                border: "1px solid #1F1F1F",
              }}
              variant="outlined"
              color="inherit"
            >
              <Github /> Connect with Github
            </Button>
          </Link>
          <Typography className="text-tx-primary font-manrope font-semibold md:w-28 text-sm">
            & Deploy your site in seconds
          </Typography>
        </Box>
      </Box>
    </motion.div>
  );
}

export default ConnectGithub;
