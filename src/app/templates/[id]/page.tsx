// app/templates/[id]/page.tsx
import { TemplateContent } from "@/redux/slices/templateSlice";
import apiClient from "@/utils/interceptor";
import TemplateContentClient from "./TemplateContentClient";
import InterceptorStaging from "@/utils/staging-interceptor";
import Head from "next/head";
import { Metadata } from "next";

interface PageProps {
  params: { id: string };
}

export async function generateMetadata({
  params,
}: // searchParams,
{
  params: { id: string };
}): Promise<Metadata> {
  const templateContent = await fetchTemplateData(params?.id);

  return {
    title: {
      absolute: templateContent?.page_title || "Template Page",
    },
    description:
      templateContent?.meta_description ||
      "Default description for the template page.",
    alternates: {
      canonical: "https://kuberns.com/templates/" + params?.id,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

async function fetchTemplateData(id: string): Promise<TemplateContent | null> {
  try {
    const res = await apiClient.get(
      `open-source-templates/list/${id}`
    );
    return res.data.data || null;
  } catch (error) {
    return null;
  }
}

export async function generateStaticParams() {
  try {
    const res = await apiClient.get("open-source-templates/preview");
    return res.data.data.map((template: any) => ({
      id: template.public_id,
    }));
  } catch (error) {
    return [];
  }
}

export default async function TemplateContentPage({ params }: PageProps) {
  const templateContent = await fetchTemplateData(params.id);

  if (!templateContent) {
    return <div>Template not found</div>;
  }

  return (
    <>
      <Head>
        <title>{templateContent.page_title || "Template Page"}</title>
        <meta
          name="description"
          content={
            templateContent.meta_description ||
            "Default description for the template page."
          }
        />
      </Head>
      <TemplateContentClient templateContent={templateContent} />
    </>
  );
}
