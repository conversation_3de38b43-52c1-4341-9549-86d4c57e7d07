import { getTemplates, Template } from "@/redux/slices/templateSlice";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { Box, Divider, Typography, useMediaQuery } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

function RelatedOpenSource({
  currentTemplateId,
}: {
  currentTemplateId: string;
}) {
  const dispatch = useAppDispatch();

  const { templates } = useAppSelector((state) => state.template);

  const [relatedTemplates, setRelatedTemplates] = useState<Template[]>([]);

  useEffect(() => {
    dispatch(getTemplates());
  }, []);

  useEffect(() => {
    if (templates.length > 0) {
      const x = templates?.filter(
        (item) => item?.public_id !== currentTemplateId
      );
      setRelatedTemplates(x);
    }
  }, [templates]);

  if (relatedTemplates.length === 0) return null;

  return (
    <Box className="flex flex-col items-center justify-center w-full gap-5">
      <Box className="flex  items-center justify-between w-full">
        <Typography className="text-[#D4D4D4 text-xl font-semibold]">
          Related Open Source
        </Typography>
        <Link href={"/templates"} className="text-[#727272] text-sm">
          View All
        </Link>
      </Box>
      <Divider
        style={{
          width: "100%",
          backgroundColor: "#232323",
        }}
      />
      <Box className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 w-full overflow-x-auto items-center justify-start gap-5">
        {relatedTemplates.map((item) => {
          return (
            <FeatureCard
              key={item.public_id}
              name={item.main_header}
              description={item.short_description}
              icon={item.logo_url}
              id={item.public_id}
            />
          );
        })}
      </Box>
    </Box>
  );
}

export default RelatedOpenSource;
const FeatureCard = ({
  description,
  name,
  icon,
  id,
}: {
  name: string;
  description: string;
  icon: string;
  id: string;
}) => {
  const isMobile = useMediaQuery("(max-width: 600px)");

  return (
    <Link href={`/templates/${id}`} className="flex flex-col justify-start items-start w-full gap-5 border border-[#232323] bg-[#111] rounded-lg p-5">
      <Box className="flex items-center w-full gap-3">
        <Image
          src={icon}
          alt={name}
          width={isMobile ? 40 : 80}
          height={isMobile ? 40 : 80}
        />
        <Typography className="text-[#D4D4D4] text-lg font-medium">
          {name}
        </Typography>
      </Box>
      <Typography className="text-[#9E9E9E] text-base font-normal">
        {description}
      </Typography>
    </Link>
  );
};
