import { TemplateContent } from "@/redux/slices/templateSlice";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import { ReplaceItemPill } from "../../components/TemplateCard";
import FeatureCard from "./FeatureCard";
import Sidebar from "./Sidebar";

function MainContent({
  templateContent,
}: {
  templateContent: TemplateContent;
}) {
  const {
    alternatives,
    features_description,
    hero_section_logo_url,
    main_header,
    notes,
    sub_header,
    sub_header_description,
    type_of_features,
    youtube_url,
    open_source_template_url,
    notes_header,
    notes_description,
    features_header,
  } = templateContent;

  return (
    <Box className="flex flex-col w-full items-center justify-start lg:w-[80%] h-full gap-6 lg:gap-10">
      <Box className="flex flex-col lg:flex-row items-start lg:items-center justify-start gap-5 lg:gap-10 w-full">
        <Typography>Popular Alternative for</Typography>
        <Box className="flex items-center justify-start gap-2">
          {alternatives.map((item, index) => (
            <ReplaceItemPill key={index} item={item} />
          ))}
        </Box>
      </Box>
      <Box className="lg:hidden block">
        <Sidebar youtube_url={youtube_url} />
      </Box>
      <Box className=" p-5 lg:p-10 bg-[#111] rounded-3xl flex justify-center items-center flex-col">
        <Image
          src={hero_section_logo_url}
          alt="logo"
          width={1000}
          height={140}
          className="rounded-xl"
        />
        <Link
          href={open_source_template_url}
          target="_blank"
          className="text-[#9E9E9E] text-center text-sm font-bold pt-5"
        >
          Visit {main_header}
        </Link>
      </Box>
      <Box className="flex flex-col w-full text-left gap-5 pt-0 lg:pt-10">
        <h1 className="text-[#D4D4D4] text-xl lg:text-2xl font-medium">{sub_header}</h1>
        <h2 className="text-[#9E9E9E] text-base font-normal">
          {sub_header_description}
        </h2>
      </Box>
      <Box className="flex flex-col justify-start items-start w-full gap-5">
        <Typography className="text-[#D4D4D4] text-base lg:text-lg font-medium">
          {notes_header}:
        </Typography>
        <Typography className="text-[#9E9E9E] text-sm lg:text-base font-normal">
          {notes_description}
        </Typography>
        <Box className="flex flex-col justify-start items-start w-full pl-5">
          <ol className="text-[#9E9E9E] text-sm lg:text-base font-normal list-disc list-inside flex flex-col w-full gap-6">
            {notes.map((item, index) => (
              <li key={index}>
                <span className="text-[#D4D4D4]">{item.split(":")[0]}:</span>
                {item.split(":")[1]}
              </li>
            ))}
          </ol>
        </Box>
      </Box>
      <Box className="flex flex-col justify-start items-start w-full gap-5">
        <Typography className="text-[#D4D4D4] text-xl lg:text-2xl font-medium">
          {features_header}
        </Typography>
        <Typography className="text-[#9E9E9E] text-sm lg:text-base font-normal">
          {features_description}
        </Typography>
        <Box className="grid grid-cols-2 lg:grid-cols-3 items-stretch gap-5 pt-5">
          {type_of_features.map((item, index) => (
            <FeatureCard key={index} feature={item} />
          ))}
        </Box>
      </Box>
    </Box>
  );
}

export default MainContent;
