import JoinNow from "@/components/nav/components/JoinNow";
import { Calculator, Youtube, Docs } from "@/constants/icons";
import { Chat } from "@mui/icons-material";
import { Box, Typography, Divider } from "@mui/material";
import Link from "next/link";
import KubernsAssured from "./KubersAssured";

const Sidebar = ({
  youtube_url,
}: {
  youtube_url: string;
}) => {
  const handlePricingCalcClick = () => {
    if (typeof window !== undefined) {
      const element = document.getElementById("pricing-calc");
      if (element) {
        const y = element.getBoundingClientRect().top + window.scrollY;
        window.scrollTo({ top: y, behavior: "smooth" });
      }
    }
  };

  return (
    <Box
      className={`flex flex-col items-center justify-center w-full h-auto relative  gap-5`}
    >
      <Box className="flex flex-col justify-center items-center w-full gap-3">
        <JoinNow
          size="medium"
          className="h-full w-full p-0"
          text={"Book a Demo Now"}
          link="/contact"
          target="_self"
        />
        <Box className="flex justify-center items-center w-full text-left max-w-full pb-2 lg:pb-5">
          <Typography className="flex items-center justify-start text-sm">
            <span className="text-[#9E9E9E]">
              With kuberns’ AI automate (deployment) the whole process with just
              one click
            </span>
          </Typography>
        </Box>
      </Box>
      <Box className="flex flex-row lg:flex-col w-full items-center justify-center gap-5">
        <div
          onClick={handlePricingCalcClick}
          className="flex items-center justify-center gap-2 w-full rounded-xl border border-[#232323] px-3 lg:px-5 py-3 cursor-pointer"
          style={{
            background: "linear-gradient(0deg, #000 0%, #3E3E3E 99.14%)",
          }}
        >
          <Calculator />
          <Typography className="text-[#D4D4D4] text-xs lg:text-base font-normal">
            Pricing Calculator
          </Typography>
        </div>
        {youtube_url && (
          <>
            <Divider
              className="w-full hidden lg:block"
              style={{
                width: "100%",
                border: "1px solid #232323",
              }}
            />
            <Link
              href={youtube_url}
              className="flex flex-col justify-start items-start w-full gap-2 border border-[#232323] bg-[#111] rounded-xl p-3 lg:p-5 cursor-pointer"
            >
              <Typography className="text-[#D4D4D4] text-xs lg:text-base font-medium flex items-center justify-start gap-2">
                <Youtube />
                Watch a 1 min Demo
              </Typography>
              <Typography className="text-[#727272] text-base font-normal hidden lg:block">
                Watch how you can deploy it in One click (Watch how it works)
              </Typography>
            </Link>
          </>
        )}
      </Box>
      <Divider
        style={{
          width: "100%",
          border: "1px solid #232323",
        }}
      />
      <Box className="flex lg:flex-col items-center lg:items-start justify-around lg:justify-start gap-3 w-full">
        {/* <Link
          href={"https://docs.kuberns.com/"}
          className="flex items-center justify-start gap-2"
        >
          <Docs />
          <Typography className="text-[#9E9E9E] text-base font-normal">
            Docs
          </Typography>
        </Link> */}
        <Link href={"#"} className="flex items-center justify-start gap-2">
          <Chat />
          <Typography className="text-[#9E9E9E] text-base font-normal">
            Support
          </Typography>
        </Link>
      </Box>
    </Box>
  );
};

export default Sidebar;
