import { Box, Typography } from "@mui/material";

const FeatureCard = ({
  feature,
}: {
  feature: { name: string; description: string };
}) => {
  return (
    <Box className="flex flex-col justify-start items-start w-full gap-5 border border-[#232323] bg-[#111] rounded-lg p-5">
      <Typography className="text-[#D4D4D4] text-base lg:text-lg font-medium">
        {feature.name}
      </Typography>
      <Typography className="text-[#9E9E9E] text-sm lg:text-base font-normal">
        {feature.description}
      </Typography>
    </Box>
  );
};

export default FeatureCard;
