"use client";

import PageLayout from "@/components/Layout";
import { TemplateContent } from "@/redux/slices/templateSlice";
import { ArrowBack } from "@mui/icons-material";
import { Box, IconButton, Typography, useMediaQuery } from "@mui/material";
import { Manrope } from "next/font/google";
import PricingCalc from "../components/PricingCalc";
import styles from "../template.module.css";
import MainContent from "./components/MainContent";
import RelatedOpenSource from "./components/RelatedOpenSource";
import Sidebar from "./components/Sidebar";
import Image from "next/image";
import KubernsAssured from "./components/KubersAssured";

const manrope = Manrope({ subsets: ["latin"] });

function TemplateContentClient({
  templateContent,
}: {
  templateContent: TemplateContent;
}) {
  const { main_header, youtube_url, logo_url, short_description, h1, h2 } =
    templateContent;

  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <main
      className={`flex flex-col items-center justify-start ${manrope.className} ${styles.bgNoiseImage} relative`}
    >
      {h1?.map((item, index) => (
        <h1 className="hidden-heading">{item} </h1>
      ))}
      {h2?.map((item, index) => (
        <h2 className="hidden-heading">{item} </h2>
      ))}
      <PageLayout className="flex items-center justify-start w-full pt-28 lg:pt-36">
        <IconButton
          onClick={() => window.history.back()}
          className="text-[#9E9E9E] text-sm font-medium rounded-xl flex items-center justify-center gap-2 hover:bg-transparent"
        >
          <ArrowBack /> <span>Back</span>
        </IconButton>
      </PageLayout>
      <PageLayout>
        <Box className="flex items-center justify-start gap-3 lg:gap-5 w-full h-full">
          <Box className=" p-3 lg:p-5 bg-[#111] rounded-lg flex justify-center items-center">
            <Image
              src={logo_url}
              alt="logo"
              width={isMobile ? 50 : 100}
              height={isMobile ? 50 : 100}
            />
          </Box>
          <Box className="flex flex-col justify-center items-start w-full gap-2">
            <Box className="flex justify-between items-center w-full flex-col gap-2">
              <Box className="flex w-full items-center justify-between">
                <Typography className="text-[#D4D4D4] text-xl lg:text-2xl font-bold">
                  {main_header}
                </Typography>
                <KubernsAssured />
              </Box>
              <Box className="flex w-full items-center justify-between">
                <Typography className="text-[#9E9E9E] text-xs lg:text-sm font-medium">
                  {short_description}
                </Typography>
                <Typography className="text-[#9E9E9E] text-sm font-normal max-w-[300px] text-right lg:flex hidden">
                  Tested, verified & optimized by Kuberns for faster & smoother
                  deployment
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </PageLayout>
      <PageLayout
        className={`w-full flex-col lg:flex-row h-auto flex justify-start items-start pt-5 z-10 gap-10`}
        id="pricing-content"
      >
        <MainContent templateContent={templateContent} />

        <Box
          className="hidden lg:flex justify-end w-[30%] pl-20"
          style={{
            position: "sticky",
            top: "10px",
            zIndex: 10,
            alignSelf: "start",
          }}
        >
          <Sidebar youtube_url={youtube_url} />
        </Box>
      </PageLayout>

      <PageLayout className="flex flex-col items-center lg:items-start justify-start">
        <PricingCalc
          isFromTemplateContent={true}
          templateContent={templateContent}
        />
      </PageLayout>

      <PageLayout className="flex flex-col items-center lg:items-start justify-start">
        <RelatedOpenSource currentTemplateId={templateContent.public_id} />
      </PageLayout>
    </main>
  );
}
export default TemplateContentClient;
