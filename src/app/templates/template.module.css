.bgImage {
  position: relative;
  min-height: 80vh;
  background: none;
}

.bgImage::before {
  content: "";
  position: absolute;
  top: -120px;
  width: 100%;
  height: 1016.945px;
  background-image: url("https://dh0pnjwiz7wby.cloudfront.net/public/filler.svg");
  background-size: contain;
  background-position: top;
  background-repeat: no-repeat;
  z-index: -1;
}

.bgNoiseImage {
  position: relative;
  min-height: 80vh;
  background: none;
  overflow: hidden;
  /* Ensures no overflow from children */
}

.bgNoiseImage::before {
  content: "";
  position: absolute;
  width: 100vw;
  height: 100vh;
  background-image: radial-gradient(
    circle at center 80%,
    rgba(0, 0, 0, 0) 40%,
    rgba(0, 0, 0, 1) 100%
  );
  /* url("https://dh0pnjwiz7wby.cloudfront.net/public/gridbg.svg"); */

  background-size: 100%, 100%;
  /* Ensure gradient and image align */
  background-position: center, center;
  background-repeat: no-repeat, no-repeat;
  z-index: -1;
  opacity: 0.7;
  /* Adjust opacity of the effect */
  mix-blend-mode: lighten;
  /* Makes it blend nicely */
}

@media (max-width: 767px) {
  .bgImage::before {
    background-size: 200%;
    background-position: top;
    top: -45px;
  }

  .bgNoiseImage::before {
    width: 1000px;
    background-image: radial-gradient(
      circle at center 20%,
      rgba(0, 0, 0, 0) 50%,
      rgba(0, 0, 0, 1) 100%
    );
    /* url("https://dh0pnjwiz7wby.cloudfront.net/public/gridbg.svg"); */
    background-position: right;
  }
}

.italic_gradient_text {
  background: conic-gradient(
    from 180deg at 48.5% 50%,
    #fbfbfd 26.24999910593033deg,
    #c8d4da 88.12500178813934deg,
    #fff 156.58468008041382deg,
    #aec0ce 191.74442768096924deg,
    #e3e9ee 237.1290135383606deg,
    #fafbfc 255.19062280654907deg,
    #d6dfe6 310.1085305213928deg,
    #b8c9d3 331.875deg
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-family: "DM Serif Text";
  font-size: 16px;
  font-style: italic;
  font-weight: 600;
}
