import { Server } from "@/constants/icons";
import { LOGO } from "@/constants/vars";
import {
  Team,
  TeamDetails,
  TemplateContent,
} from "@/redux/slices/templateSlice";
import { useAppSelector } from "@/redux/store";
import { Box, Divider, Slider, Typography, useMediaQuery } from "@mui/material";
import { motion, useInView } from "framer-motion";
import * as _ from "lodash";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

function PricingCalc({
  isFromTemplateContent,
  templateContent,
}: {
  isFromTemplateContent?: boolean;
  templateContent?: TemplateContent;
}) {
  const { templates } = useAppSelector((state) => state.template);

  const [selectedTeam, setSelectedTeam] = useState<Team>({} as Team);

  const [selectedPlan, setSelectedPlan] = useState<"monthly" | "yearly">(
    "monthly"
  );

  const isMobile = useMediaQuery("(max-width: 767px)");

  const [marksForTemplate, setMarksForTemplate] = useState<
    { value: number; label: string }[]
  >([]);

  const [selectedTemplate, setSelectedTemplate] = useState<string>("");

  const findTemplate = templates?.find(
    (template) => template?.public_id === selectedTemplate
  );

  const getNumericMemory = (memory?: string): number => {
    if (!memory || memory.length < 3) return 0; // Basic guard

    // Extract the numeric part and the unit (GB or TB)
    const numericValue = parseFloat(memory);
    const unit = memory.slice(-2).toUpperCase();

    // Convert to GB
    if (unit === "TB") {
      return numericValue * 1024; // 1 TB = 1024 GB
    } else if (unit === "GB") {
      return numericValue;
    } else {
      return 0; // Handle cases where unit is unexpected
    }
  };

  const calculateLessPercentage = ({
    original,
    rest,
  }: {
    original: number;
    rest: number[];
  }) => {
    const restTotal = rest?.reduce((a, b) => a + b, 0);

    const percentLess = (((restTotal - original) / restTotal) * 100)?.toFixed(
      0
    );

    return percentLess + "%";
  };

  useEffect(() => {
    const extractMarks = (teams?: Team[]) => {
      return (teams || []).map((team, index) => ({
        value: index, // Now returning values in GB
        label: team?.memory,
      }));
    };

    if (!isFromTemplateContent) {
      const template = templates.find((t) => t.public_id === selectedTemplate);

      if (template) {
        const marks = extractMarks(template?.pricing_calculator?.teams);

        setMarksForTemplate(marks);
        setSelectedTeam(template?.pricing_calculator?.teams[0]);
      }
    } else if (templateContent) {
      const marks = extractMarks(templateContent?.pricing_calculator?.teams);
      setSelectedTeam(templateContent?.pricing_calculator?.teams[0]);
      setMarksForTemplate(marks);
    }
  }, [isFromTemplateContent, selectedTemplate, templates, templateContent]);

  useEffect(() => {
    if (!isFromTemplateContent) {
      if (templates?.length > 0) {
        setSelectedTemplate(templates[0]?.public_id);
        setSelectedTeam(templates[0]?.pricing_calculator?.teams[0]);
      }
    } else {
      setSelectedTemplate(templateContent?.public_id || "");
      setSelectedTeam(
        templateContent?.pricing_calculator?.teams[0] || ({} as Team)
      );
    }
  }, [templates, isFromTemplateContent, templateContent]);

  const selectedPlanString = selectedPlan === "monthly" ? "Month" : "Year";

  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { amount: 1, once: true });

  return (
    <Box
      className={`flex flex-col items-start justify-center py-5 lg:py-10 h-full ${
        isFromTemplateContent ? "w-full" : "w-full lg:w-[90%]"
      }`}
      ref={containerRef}
      id="pricing-calc"
    >
      <Typography className="text-white text-xl font-manrope font-medium pb-5">
        Pricing Calculator
      </Typography>
      <Box
        className="w-full relative h-full rounded-3xl border border-[#232323]"
        style={{
          background:
            "radial-gradient(309.32% 50% at 50% 50%, rgba(22, 143, 249, 0.50) 0%, rgba(0, 26, 89, 0.50) 100%)",
        }}
      >
        <Box className="flex flex-col lg:flex-row   w-full h-full rounded-3xl bg-[#111111] border border-b-2 border-[#232323] p-5 lg:p-10 gap-5">
          <Box className="w-full lg:w-[70%] flex flex-col items-start justify-start gap-3">
            {!isFromTemplateContent && (
              <Box className="flex items-center justify-start gap-5 overflow-x-auto w-full">
                {templates.map((template) => {
                  return (
                    <Box
                      onClick={() => setSelectedTemplate(template.public_id)}
                      className={`p-[1px] rounded-xl min-w-fit text-nowrap ${
                        selectedTemplate === template.public_id &&
                        "bg-[conic-gradient(from_60deg_at_50%_50%,#0538FF_13.4%,#6B57F5_86.6%)]"
                      } hover:bg-[conic-gradient(from_60deg_at_50%_50%,#0538FF_13.4%,#6B57F5_86.6%)] transition-all duration-200`}
                    >
                      <Box className="flex gap-2 items-center justify-center rounded-xl py-2 px-5 bg-[#1B1B1B] cursor-pointer border border-[#232323]">
                        <Image
                          src={template.logo_url || LOGO}
                          alt={template?.main_header}
                          width={30}
                          height={30}
                        />
                        <Typography
                          className={`text-lg font-manrope font-medium text-nowrap ${
                            selectedTemplate === template.public_id
                              ? "text-[#006AFF]"
                              : "text-white"
                          }`}
                        >
                          {template.main_header}
                        </Typography>
                      </Box>
                    </Box>
                  );
                })}
              </Box>
            )}
            {isFromTemplateContent && (
              <Box className="flex items-center justify-start gap-2">
                <Image
                  src={templateContent?.logo_url || LOGO}
                  alt={templateContent?.main_header || ""}
                  width={60}
                  height={60}
                />
                <Typography className="text-[#D4D4D4] text-xl lg:text-2xl font-manrope font-medium">
                  {templateContent?.main_header} Pricing Calculator
                </Typography>
              </Box>
            )}
            <Typography className="text-[#727272] text-sm font-manrope font-medium">
              Adjust the slider to your server requirements to calculate the
              cost.
            </Typography>
            <Box className="flex justify-between w-full items-center pt-3 lg:pt-5">
              <Box className="flex items-center gap-2">
                <Server />
                <Typography className="text-[#9E9E9E] text-sm lg:text-lg font-manrope font-medium">
                  Server
                </Typography>
              </Box>
              <Typography className="text-[#D4D4D4] text-lg lg:text-2xl font-manrope font-medium">
                32 GB
              </Typography>
            </Box>
            {marksForTemplate.length > 0 && (
              <Box sx={{ width: "100%" }}>
                <Box className="block md:hidden">
                  <Box className="flex flex-col w-full items-start justify-start gap-2 pt-3">
                    <Typography className="text-[#727272] text-xs">
                      Suited For
                    </Typography>
                    <Box className="flex justify-between items-center w-full">
                      <Typography className="text-[#D4D4D4] text-sm">
                        {selectedTeam.type}
                      </Typography>
                      <Typography className="text-[#727272] text-sm">
                        {selectedTeam.members} members
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                <Slider
                  min={marksForTemplate?.[0]?.value}
                  max={marksForTemplate?.[marksForTemplate.length - 1]?.value}
                  marks={marksForTemplate?.map((mark) => ({
                    value: mark.value,
                    label: mark.label,
                  }))}
                  valueLabelDisplay={isMobile ? "auto" : "on"}
                  valueLabelFormat={(value) => {
                    return (
                      <Box className="flex flex-col items-center gap-1 w-full">
                        <Typography className="text-sm flex items-center justify-between gap-2 w-full">
                          <span className="text-left">Server Config:</span>
                          <span>{selectedTeam.server_config}</span>
                        </Typography>
                        <Typography className="text-sm flex items-center justify-between gap-2 w-full">
                          <span className="text-left">Storage Config:</span>
                          <span>{selectedTeam.storage_config}</span>
                        </Typography>
                      </Box>
                    );
                  }}
                  defaultValue={0}
                  step={null}
                  onChange={(e, value) => {
                    const team =
                      findTemplate?.pricing_calculator?.teams[value as any] ||
                      ({} as Team);
                    setSelectedTeam(team);
                  }}
                  value={findTemplate?.pricing_calculator?.teams?.findIndex(
                    (item) => item.memory === selectedTeam.memory
                  )} // Use normalized memory value
                  sx={{
                    color: "transparent",
                    height: 6,
                    padding: "15px 0",

                    "& .MuiSlider-track": {
                      backgroundColor: "#2766FE",
                      border: "none",
                      padding: "5px",
                    },
                    "& .MuiSlider-rail": {
                      backgroundColor: "#272727",
                      opacity: 1,
                      padding: "5px",
                    },
                    "& .MuiSlider-thumb": {
                      width: 20,
                      height: 20,
                      background:
                        "radial-gradient(50% 50% at 50% 50%, #D9D9D9 0%, #FFF 100%)",
                      border: "1px solid #000",
                      boxShadow:
                        "0px 0.938px 1.875px rgba(16, 24, 40, 0.06), 0px 1.875px 3.75px rgba(16, 24, 40, 0.10)",
                      "&:hover, &.Mui-focusVisible, &.Mui-active": {
                        boxShadow:
                          "0px 0.938px 1.875px rgba(16, 24, 40, 0.1), 0px 1.875px 4.75px rgba(16, 24, 40, 0.12)",
                      },
                    },

                    // ✅ First mark at 1%
                    [`& .MuiSlider-mark[data-index="0"]`]: {
                      left: "1% !important",
                    },

                    // ✅ Last mark at 99%
                    [`& .MuiSlider-mark[data-index="${
                      marksForTemplate.length - 1
                    }"]`]: {
                      left: "99% !important",
                    },

                    // ✅ Custom Tick
                    "& .MuiSlider-mark": {
                      width: 5,
                      height: 5,
                      borderRadius: "50%",
                      backgroundColor: "#fff",
                      opacity: 1,
                      transform: "translate(-50%, -50%)", // center the circle
                      flexShrink: 0,
                    },

                    "& .MuiSlider-markLabel": {
                      color: "#fff",
                      fontSize: 12,
                      bottom: "100%", // position above the slider
                      marginBottom: "8px", // space between label and track
                      transform: "translateX(-50%)",
                    },
                  }}
                />
                <Box className="hidden md:block">
                  {isFromTemplateContent ? (
                    <ScrollbarBottomDetails
                      teamDetails={
                        templateContent?.pricing_calculator.teamDetails || []
                      }
                      totalTeams={
                        templateContent?.pricing_calculator?.teams?.length || 0
                      }
                    />
                  ) : (
                    <ScrollbarBottomDetails
                      teamDetails={
                        findTemplate?.pricing_calculator.teamDetails || []
                      }
                      totalTeams={
                        findTemplate?.pricing_calculator?.teams?.length || 0
                      }
                    />
                  )}
                </Box>
              </Box>
            )}
          </Box>
          <Divider
            sx={{
              backgroundColor: "#272727",
              margin: "0 20px",
            }}
            orientation="vertical"
            flexItem
            className="hidden lg:block"
          />
          <Box className=" w-full lg:w-[30%] flex flex-col items-start justify-start gap-5">
            <Box className="flex flex-col items-start justify-start gap-5 bg-[#1B1B1B] rounded-3xl  w-full">
              <Box className="flex items-center justify-around w-full rounded-3xl bg-[#313131] p-2">
                <Box
                  onClick={() => setSelectedPlan("monthly")}
                  className={`text-[#727272] text-lg font-manrope font-medium p-3 rounded-2xl w-full flex items-center justify-center cursor-pointer transition-all duration-200 ${
                    selectedPlan === "monthly" && "text-[#FFF] bg-[#1B1B1B]"
                  }`}
                >
                  Monthly
                </Box>
                <Box
                  onClick={() => setSelectedPlan("yearly")}
                  className={`text-[#727272] text-lg font-manrope font-medium p-3 rounded-2xl w-full flex items-center justify-center cursor-pointer transition-all duration-200 ${
                    selectedPlan === "yearly" && "text-[#FFF] bg-[#1B1B1B]"
                  }`}
                >
                  Yearly
                </Box>
              </Box>
              <Box
                className="flex w-full items-center justify-center pb-5"
                style={{
                  fontFamily: "DM Sans",
                }}
              >
                <Typography className="text-[#FFF] text-4xl font-manrope font-medium flex items-center justify-center w-full px-5">
                  <span className="text-[#727272] pr-2">$</span>
                  <span>
                    {selectedTeam?.[selectedPlan]}
                    <span className="text-[#727272]">.00</span>
                  </span>
                </Typography>
              </Box>
            </Box>
            <Box className="flex flex-col items-start justify-start gap-1 w-full">
              <Box className="grid grid-cols-2 items-center justify-between gap-5 w-full">
                <Typography className="text-[#FFF] text-sm font-manrope font-medium flex-1">
                  That’s{" "}
                  {calculateLessPercentage({
                    original: selectedTeam?.[selectedPlan],
                    rest: selectedTeam?.comparison?.map(
                      (item) => item?.[selectedPlan]
                    ),
                  })}{" "}
                  less{" "}
                </Typography>
                <Typography className="text-[#727272] text-xs font-manrope font-medium flex-1">
                  In Comparison with
                </Typography>
              </Box>

              {selectedTeam?.comparison?.map((item) => {
                return (
                  <>
                    <Box className="grid grid-cols-2 items-center justify-between gap-5 w-full pt-2">
                      <Typography className="text-[#FFF] text-sm font-manrope font-medium flex-1 flex items-center gap-2">
                        <Image
                          src={item.logo_url}
                          width={20}
                          height={20}
                          alt={item.name}
                        />
                        {item?.name}
                      </Typography>
                      <Typography className=" text-base font-manrope font-medium flex-1 flex items-center gap-1">
                        <span className="text-[#fff]">
                          $ {item?.[selectedPlan]}
                        </span>
                        <span className="text-[#727272]">
                          /{selectedPlanString}
                        </span>
                      </Typography>
                    </Box>
                  </>
                );
              })}
            </Box>
          </Box>
        </Box>
        {isInView && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={
              isInView
                ? { height: "60px", opacity: 1 }
                : { height: "0px", opacity: 0 }
            }
            transition={{
              type: "spring",
              stiffness: 120,
              damping: 14,
              duration: 1,
            }}
            className="overflow-hidden flex items-center justify-between rounded-b-3xl w-full py-5 px-5 lg:px-10"
          >
            <Typography className=" text-xs md:text-lg font-manrope font-medium">
              Unlimited Users Forever
            </Typography>
            <Typography className="text-xs md:text-lg font-manrope font-medium">
              🛑 No Per user Pricing
            </Typography>
          </motion.div>
        )}
      </Box>
    </Box>
  );
}

export default PricingCalc;

const ScrollbarBottomDetails = ({
  teamDetails,
  totalTeams,
}: {
  teamDetails: TeamDetails[];
  totalTeams: number;
}) => {
  return (
    <Box className="flex items-center justify-start w-full">
      {teamDetails?.map((item, index) => {
        if (index !== totalTeams - 1)
          return (
            <Box className="w-full flex flex-col justify-center items-center gap-1 text-base">
              <Typography className=" text-left text-[#D4D4D4]">
                {item.title}
              </Typography>
              <Typography className=" w-[100px] text-center text-[#727272]">
                {item.description}
              </Typography>
            </Box>
          );
      })}
    </Box>
  );
};
