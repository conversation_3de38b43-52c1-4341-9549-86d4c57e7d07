import { Box, Typography } from "@mui/material";
import React from "react";
import { LOG<PERSON> } from "@/constants/vars";
import Link from "next/link";

function Hero() {
  return (
    <Box className="flex flex-col items-center justify-center h-full gap-5 w-full lg:w-3/5">
      <Box className="flex justify-center items-center gap-2 lg:gap-2 no-underline text-inherit">
        <img
          src={LOGO}
          alt="Logo Kuberns"
          className="w-auto h-8"
          title="Deploy your backend in one click with <PERSON><PERSON><PERSON>"
        />
        <Typography
          className={`text-sm lg:text-xl font-manrope font-medium gradient-text tracking-wide text-[#005BFC]`}
        >
          Kuberns
        </Typography>
      </Box>
      <Typography className="text-2xl lg:text-3xl font-manrope font-medium gradient_text text-center">
        Open Source Marketplace 
      </Typography>
      <Typography className="text-sm font-manrope font-medium text-center text-[#727272]">
        Discover a curated selection of templates to effortlessly deploy and
        manage your favorite self-hosted services on our platform, all powered
        by open-source flexibility.
      </Typography>
    </Box>
  );
}

export default Hero;
