import { Box, Typography } from "@mui/material";
import React from "react";
import styles from "../template.module.css";
import {
  REDIRECT_ARROW,
  SOLAR_MEDAL_RIBBON_START_BOLD,
  SOLAR_MEDAL_STAR_BOLD,
  SOLAR_MODAL_STAR_SQUARE_BOLD,
} from "@/constants/vars";
import Image from "next/image";
import { motion } from "framer-motion";

function WhyKuberns() {
  return (
    <Box className="flex flex-col lg:flex-row items-center justify-start w-[90%] gap-5">
      <Typography
        className={`${styles.italic_gradient_text} text-xl font-manrope font-medium w-full lg:w-[200px] flex flex-row lg:flex-col gap-1`}
      >
        <span className="text-nowrap">Why Innovators</span>
        <span>choose</span>
        <span>Kuberns?</span>
      </Typography>
      <Box className="grid grid-cols-1 lg:grid-cols-2 gap-5 w-full">
        {X?.map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: -40 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.4,
              delay: index * 0.12,
              ease: "easeOut",
            }}
            className={`w-full ${
              index === 2 ? "lg:col-span-2 lg:grid lg:justify-center" : ""
            }`}
          >
            <Box
              className="flex items-center justify-start gap-2 rounded-xl border border-dashed border-[#333] px-3 py-2 w-full"
              style={{
                background:
                  "linear-gradient(90deg, rgba(25, 25, 25, 0.15) 0%, rgba(17, 17, 17, 0.15) 100%)",
                backdropFilter: "blur(6.074999809265137px)",
              }}
            >
              <Image
                src={item?.icon}
                alt={item?.title}
                width={20}
                height={20}
              />
              <Typography className="text-base">{item?.title}</Typography>
            </Box>
          </motion.div>
        ))}

        {/* Static last item with arrow */}
        {/* <motion.div
          initial={{ opacity: 0, x: -40 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{
            duration: 0.4,
            delay: X?.length * 0.12,
            ease: "easeOut",
          }}
          className="flex items-center justify-center lg:justify-start w-full"
        >
          <Box className="flex gap-2 items-center text-[#9E9E9E] text-sm font-manrope font-medium">
            & more{" "}
            <Image src={REDIRECT_ARROW} alt="arrow" width={15} height={15} />
          </Box>
        </motion.div> */}
      </Box>
    </Box>
  );
}

export default WhyKuberns;

const X = [
  {
    id: 1,
    title: "Complete Control Over Domains, Ports, and Environments",
    icon: SOLAR_MEDAL_RIBBON_START_BOLD,
  },
  {
    id: 2,
    title: "Host on Your Own Server or VPS with Zero Hidden Costs",
    icon: SOLAR_MEDAL_STAR_BOLD,
  },
  {
    id: 3,
    title: "AI-Assisted Setup That Deploys Your App 50% Faster",
    icon: SOLAR_MODAL_STAR_SQUARE_BOLD,
  },
];
