import { useAppSelector } from "@/redux/store";
import { Box } from "@mui/material";
import { motion } from "framer-motion";
import "swiper/css";
import "swiper/css/effect-cards";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import {
	EffectCards,
	Pagination
} from "swiper/modules"; // Import EffectCoverflow
import { Swiper, SwiperSlide } from "swiper/react";
import TemplateCard from "./TemplateCard";

function MobileSwiperCards() {
  const { templates } = useAppSelector((state) => state.template);

  return (
    <Box className={"pt-0 lg:pt-10 w-full"}>
      {templates?.length > 0 && (
        <Swiper
          effect={"cards"}
          grabCursor={true}
          modules={[EffectCards]}
          spaceBetween={50}
          slidesPerView={1}
          scrollbar={false}
          style={{
            width: "100%",
          }}
          className="swiper-container"
        >
          {templates?.map((template, index) => (
            <SwiperSlide key={template.public_id}>
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  duration: 0.5 + index * 0.15,
                  delay: index * 0.15,
                  ease: "easeOut",
                }}
                className="w-full flex items-center justify-center"
              >
                <TemplateCard template={template} />
              </motion.div>
            </SwiperSlide>
          ))}
        </Swiper>
      )}
    </Box>
  );
}

export default MobileSwiperCards;
