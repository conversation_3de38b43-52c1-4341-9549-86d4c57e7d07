import { useAppSelector } from "@/redux/store";
import { Box } from "@mui/material";
import { motion } from "framer-motion";
import React from "react";
import TemplateCard from "./TemplateCard";

function TemplateCardsWeb() {
  const { templates } = useAppSelector((state) => state.template);

  return (
    <Box
      className={`gap-5 w-[90%] pt-0 lg:pt-10 justify-center items-center flex mg:flex-wrap`}
    >
      {templates?.length > 0 &&
        templates?.map((template, index) => (
          <motion.div
            key={template.public_id}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.5 + index * 0.15,
              delay: index * 0.15, // stagger based on index
              ease: "easeOut",
            }}
            className="flex justify-center items-center"
          >
            <TemplateCard key={template?.public_id} template={template} />
          </motion.div>
        ))}
    </Box>
  );
}

export default TemplateCardsWeb;
