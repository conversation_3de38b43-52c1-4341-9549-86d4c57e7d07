import { TopRightArrow } from "@/constants/icons";
import { LOGO } from "@/constants/vars";
import { Template } from "@/redux/slices/templateSlice";
import { Box, Typography } from "@mui/material";
import { motion } from "framer-motion";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

function TemplateCard({ template }: { template: Template }) {
  const [isHovered, setIsHovered] = useState(false);
  const [cardWidth, setCardWidth] = useState<number | null>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const updateWidth = () => {
      if (cardRef.current) {
        setCardWidth(cardRef.current.offsetWidth);
      }
    };

    // Initial width on mount
    updateWidth();

    // Add resize event listener
    window.addEventListener("resize", updateWidth);

    // Cleanup on unmount
    return () => {
      window.removeEventListener("resize", updateWidth);
    };
  }, []);

  return (
    <motion.div
      className="flex flex-col items-center justify-start lg:gap-5 h-[350px] lg:h-[450px] w-[90%]  max-w-sm border border-[#232323] bg-[#111111] rounded-3xl p-5 relative cursor-pointer hover:border-[#168ff97f] transition-all duration-300"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      ref={cardRef}
      id={template?.public_id}
      onClick={() =>
        (window.location.href = `/templates/${template?.public_id}`)
      }
    >
      <Box className="flex flex-col items-center justify-start pl-1 lg:pl-0 lg:justify-center gap-2 lg:gap-5 w-full min-h-[150px] lg:min-h-[170px]">
        <Box className="flex justify-between items-center w-full pt-2">
          <Box className="flex items-center justify-start  lg:justify-center gap-2 w-full">
            <Image
              src={template.logo_url || LOGO}
              alt={template?.main_header}
              width={40}
              height={40}
            />
            <Typography className="text-white text-center font-normal text-xl lg:text-2xl">
              {template?.main_header}
            </Typography>
          </Box>
          <Box className="flex lg:hidden">
            <TopRightArrow />
          </Box>
        </Box>
        <Typography
          className={`text-[#9E9E9E] lg:text-center w-full text-left text-sm lg:text-base font-medium`}
        >
          {template?.short_description}
        </Typography>
      </Box>
      <Box className="flex flex-col items-start w-full pb-20 lg:pb-0">
        <motion.div
          initial={{ y: 40, opacity: 1 }}
          animate={isHovered ? { y: 20 } : { y: 40 }}
          transition={{ type: "tween", duration: 0.4 }}
          className="z-50"
        >
          <Typography
            className={`text-[#727272] text-left lg:text-center w-full text-[14px] font-medium`}
          >
            Replace
          </Typography>
          <Box className="flex flex-wrap items-center gap-3 justify-start lg:justify-center z-50 pt-5 pb-5">
            {template?.alternatives?.map((item) => (
              <ReplaceItemPill key={item} item={item} />
            ))}
          </Box>
        </motion.div>
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={isHovered ? { y: 0, opacity: 1 } : { y: 20, opacity: 0 }}
          transition={{ duration: 0.4 }}
          className="z-50 hidden lg:block w-full bottom-36 left-[30%] absolute"
        >
          <Typography
            className={`text-[#FFF] text-center text-xl font-medium absolute ${
              isHovered && "pt-20"
            } lg:pt-20`}
          >
            Get Started Now
          </Typography>
        </motion.div>
        <Box className="absolute bottom-0 left-0 right-0 w-full overflow-hidden rounded-b-3xl">
          <BottomSvg width={cardWidth || 500} />
        </Box>
      </Box>
    </motion.div>
  );
}

export default TemplateCard;

export const ReplaceItemPill = ({ item }: { item: string }) => {
  return (
    <Box className="rounded-[24px] px-5 py-2 cursor-pointer bg-gradient-to-b from-[rgba(90,90,90,0.5)] to-[rgba(27,27,27,0.5)] backdrop-blur-[12.5px] border border-[#232323] hover:-translate-y-2 duration-300 transition-all text-white hover:text-[#005BFC]">
      <Typography className="text-center font-normal text-xs lg:text-sm truncate">
        {item}
      </Typography>
    </Box>
  );
};

const BottomSvg = ({ width = 500 }: { width?: number }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height="180"
      viewBox="0 0 371 233"
      fill="none"
    >
      <g filter="url(#filter0_f_5193_3155)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M-152 117.142L-144.184 131.571C-135.768 146 -120.137 174.857 -103.905 174.857C-87.6724 174.857 -72.0414 146 -55.8092 127.964C-39.577 109.928 -23.946 102.714 -7.71384 91.8924C8.51836 81.0709 24.1494 66.6423 40.3816 81.0709C56.6138 95.4995 72.2448 138.785 88.4769 142.393C104.709 146 120.34 109.928 136.572 91.8924C152.805 73.8566 168.436 73.8566 184.668 84.678C200.9 95.4995 216.531 117.142 232.763 142.393C248.995 167.643 264.626 196.5 280.858 210.928C297.091 225.357 312.722 225.357 328.954 200.107C345.186 174.857 360.817 124.357 377.049 117.142C393.281 109.928 408.912 146 425.145 153.214C441.377 160.428 457.008 138.785 473.24 124.357C489.472 109.928 505.103 102.714 521.335 109.928C537.568 117.142 553.199 138.785 561.615 149.607L569.431 160.428V247H561.615C553.199 247 537.568 247 521.335 247C505.103 247 489.472 247 473.24 247C457.008 247 441.377 247 425.145 247C408.912 247 393.281 247 377.049 247C360.817 247 345.186 247 328.954 247C312.722 247 297.091 247 280.858 247C264.626 247 248.995 247 232.763 247C216.531 247 200.9 247 184.668 247C168.436 247 152.805 247 136.572 247C120.34 247 104.709 247 88.4769 247C72.2448 247 56.6138 247 40.3816 247C24.1494 247 8.51836 247 -7.71384 247C-23.946 247 -39.577 247 -55.8092 247C-72.0414 247 -87.6724 247 -103.905 247C-120.137 247 -135.768 247 -144.184 247H-152V117.142Z"
          fill="url(#paint0_radial_5193_3155)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M-152 117.142L-144.184 131.571C-135.768 146 -120.137 174.857 -103.905 174.857C-87.6724 174.857 -72.0414 146 -55.8092 127.964C-39.577 109.928 -23.946 102.714 -7.71384 91.8924C8.51836 81.0709 24.1494 66.6423 40.3816 81.0709C56.6138 95.4995 72.2448 138.785 88.4769 142.393C104.709 146 120.34 109.928 136.572 91.8924C152.805 73.8566 168.436 73.8566 184.668 84.678C200.9 95.4995 216.531 117.142 232.763 142.393C248.995 167.643 264.626 196.5 280.858 210.928C297.091 225.357 312.722 225.357 328.954 200.107C345.186 174.857 360.817 124.357 377.049 117.142C393.281 109.928 408.912 146 425.145 153.214C441.377 160.428 457.008 138.785 473.24 124.357C489.472 109.928 505.103 102.714 521.335 109.928C537.568 117.142 553.199 138.785 561.615 149.607L569.431 160.428V247H561.615C553.199 247 537.568 247 521.335 247C505.103 247 489.472 247 473.24 247C457.008 247 441.377 247 425.145 247C408.912 247 393.281 247 377.049 247C360.817 247 345.186 247 328.954 247C312.722 247 297.091 247 280.858 247C264.626 247 248.995 247 232.763 247C216.531 247 200.9 247 184.668 247C168.436 247 152.805 247 136.572 247C120.34 247 104.709 247 88.4769 247C72.2448 247 56.6138 247 40.3816 247C24.1494 247 8.51836 247 -7.71384 247C-23.946 247 -39.577 247 -55.8092 247C-72.0414 247 -87.6724 247 -103.905 247C-120.137 247 -135.768 247 -144.184 247H-152V117.142Z"
          stroke="black"
        />
      </g>
      <defs>
        <filter
          id="filter0_f_5193_3155"
          x="-227"
          y="0"
          width="871.431"
          height="322"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="37.5"
            result="effect1_foregroundBlur_5193_3155"
          />
        </filter>
        <radialGradient
          id="paint0_radial_5193_3155"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(208.715 161) rotate(180) scale(360.715 532.032)"
        >
          <stop stop-color="#168FF9" />
          <stop offset="1" stop-color="#001A59" />
        </radialGradient>
      </defs>
    </svg>
  );
};
