"use client";

import PageLayout from "@/components/Layout";
import { getTemplateContent, getTemplates } from "@/redux/slices/templateSlice";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { Manrope } from "next/font/google";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import Hero from "./components/hero";
import WhyKuberns from "./components/WhyKuberns";
import styles from "./template.module.css";
import TemplateCard from "./components/TemplateCard";
import { Box, Typography } from "@mui/material";
import PricingCalc from "./components/PricingCalc";
import { motion } from "framer-motion";
import MobileSwiperCards from "./components/TemplateCardsMobile";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import TemplateCardsWeb from "./components/TemplateCardsWeb";

const manrope = Manrope({ subsets: ["latin"] });

function TemplateClient() {
  const dispatch = useAppDispatch();
  const { templates, templateContent } = useAppSelector(
    (state) => state.template
  );
  
  const isMobile = useMediaQuery("(max-width: 600px)");

  useEffect(() => {
    dispatch(getTemplates());
  }, [dispatch]);
  useEffect(() => {
    if (templates.length > 0) {
      dispatch(getTemplateContent(templates?.[0]?.public_id));
    }
  }, [dispatch, templates]);

  return (
    <main
      className={`flex flex-col items-center justify-start ${manrope.className} ${styles.bgNoiseImage}`}
    >
      <PageLayout
        className={`w-full h-auto flex justify-center items-center flex-col pt-28 lg:pt-36 ${styles.bgImage} z-10 gap-10`}
      >
        <Hero />
        <WhyKuberns />
        <Box className="flex lg:hidden w-full items-center justify-center pt-5">
          <Typography
            className={`${styles.italic_gradient_text} text-xl font-manrope font-medium w-full text-center`}
          >
            Trending Open Source Services
          </Typography>
        </Box>

        {isMobile ? <MobileSwiperCards /> : <TemplateCardsWeb />}
        <PricingCalc isFromTemplateContent={false} />
      </PageLayout>
    </main>
  );
}

export default TemplateClient;
