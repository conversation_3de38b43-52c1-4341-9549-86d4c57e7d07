import PageLayout from "@/components/Layout";
import { List, ListItem, Typography } from "@mui/material";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: {
    absolute:
      "Terms & Conditions | Kuberns: Your AI-Cloud PaaS for Seamless Deployment",
  },
  description:
    "Kuberns offers simple, flexible pricing to meet the needs of every individual developer and every organization. Integrate GitHub, infinite CI/CD deployments and many more.",
  alternates: {
    canonical: "https://kuberns.com/terms-conditions",
  },
  robots: {
    index: false,
    follow: false,
  },
};

function TermsConditions() {
  const headingText = "#FFF";

  const innerHeadingText = "#FFF";

  const subTitleText = "#CCCCCC";

  return (
    <main
      className={`flex flex-col items-center font-manrope pt-16 md:pt-0 h-full`}
    >
      <PageLayout className="w-11/12 flex justify-center items-center flex-col pt-10 md:pt-20 gap-2">
        <Typography className="text-tx-primary font-manrope font-semibold text-2xl md:text-3xl">
          Terms & Conditions
        </Typography>
        <Typography pt={2}>
          Welcome to Kuberns! By accessing and using our services, you agree to
          comply with the following terms and conditions:
        </Typography>
        <List component="ol">
          <ListItem
            sx={{
              color: headingText,
            }}
            className="p-0 pt-3 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">1. Acceptance of Terms:</Typography>
            <ul
              className="pt-1 font-semibold pl-4"
              style={{ color: subTitleText }}
            >
              <li className="font-[13px] p-0 pl-5">
                By using Kuberns, you acknowledge that you have read,
                understood, and agree to abide by these terms and conditions.
              </li>
            </ul>
          </ListItem>
          <ListItem
            sx={{
              color: headingText,
            }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">2. User Responsibilities:</Typography>
            <List component="ol" className="font-[400]">
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">I) Integrations</Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    Kuberns can integrate with third-party services such as
                    GitHub and GitLab on your behalf.
                  </ListItem>
                </List>
              </ListItem>
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">II) Deployment</Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    You are responsible for the content and code you deploy on
                    Kuberns. Ensure compliance with all applicable laws and
                    regulations.
                  </ListItem>
                </List>
              </ListItem>
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">
                  III) Illegal Activities
                </Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    You cannot deploy illegal content, cryptocurrency miners, or
                    any other malicious software on Kuberns.
                  </ListItem>
                </List>
              </ListItem>
            </List>
          </ListItem>

          <ListItem
            sx={{
              color: headingText,
            }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">
              3. Data Security and Privacy:
            </Typography>
            <List component="ol" className="font-[400]">
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">I) Access</Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    We do not SSH into your instances and cannot access your
                    code assets.
                  </ListItem>
                </List>
              </ListItem>
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">II) Encryption</Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    All information stored on our platform is encrypted to
                    ensure your data's security.
                  </ListItem>
                </List>
              </ListItem>
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">
                  {" "}
                  III) Copyright Compliance
                </Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    We comply with copyright takedown requests as required by
                    law.
                  </ListItem>
                </List>
              </ListItem>
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">IV) Policy Changes</Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    We will notify you of any changes to these policies.
                  </ListItem>
                </List>
              </ListItem>
            </List>
          </ListItem>

          <ListItem
            sx={{
              color: headingText,
            }}
            className="p-0 pt-5 text-[15px] font-manrope font-semibold flex flex-col items-start"
          >
            <Typography component="span">4. Service Provision:</Typography>
            <List component="ol" className="font-[400]">
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">I) As-Is</Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    Our platform is provided as-is. We do not manually interfere
                    with the services provided.
                  </ListItem>
                </List>
              </ListItem>
              <ListItem
                className="font-[15px] p-0 pt-1 pl-4 flex flex-col items-start"
                sx={{ color: innerHeadingText }}
              >
                <Typography component="span">II) Transparency</Typography>
                <List
                  component="ul"
                  className="pt-1 flex flex-col"
                  sx={{ color: subTitleText }}
                >
                  <ListItem className="font-[13px] p-0 pl-5">
                    We believe in maintaining transparency with our customers
                    and will communicate any significant changes or issues.
                  </ListItem>
                </List>
              </ListItem>
            </List>
          </ListItem>
        </List>
      </PageLayout>
    </main>
  );
}

export default TermsConditions;
