import PageLayout from "@/components/Layout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, UserGroup } from "@/constants/icons";
import { <PERSON>, <PERSON>ton, Divider, Typography } from "@mui/material";
import Link from "next/link";
import styles from "./about.module.css";
import DetailsComp from "./components/Details";
import Hero from "./components/Hero";
import ValueCard from "./components/ValueCard";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: {
    absolute: "About | Kuberns: Your AI-Cloud PaaS for Seamless Deployment",
  },
  description:
    "Kuberns is a platform as a service (PaaS) that enables developers to build, run, and operate applications without any complications.",
  alternates: {
    canonical: "https://kuberns.com/about",
  },
  robots: {
    index: true,
    follow: true,
  },
};

function About() {
  return (
    <main className={`flex flex-col items-center font-manrope pt-16 md:pt-0`}>
      <PageLayout className="w-11/12 flex justify-center items-center flex-col pt-10 md:pt-10 lg:pt-20 xl:pt-24 gap-8">
        <Hero />
        <Box className="h-0 md:h-4" />
        <DetailsComp />
        <Box className="w-full md:w-8/12 flex flex-col justify-center items-start pt-5 md:pt-10 gap-1 md:gap-5">
          <h2 className={"font-manrope " + styles.about_value}>
            Our Values
          </h2>
          <Divider
            orientation="horizontal"
            sx={{
              width: "100%",
            }}
          />
          <Box className="grid grid-cols-1 md:grid-cols-3 w-full gap-5 pt-5">
            <ValueCard
              desc="Integrity is fundamental. Upholding the highest ethical standards, we foster trust and credibility with all stakeholders."
              icon={<Lock />}
              text="Integrity"
            />
            <ValueCard
              desc="Innovation propels us forward. Continuously exploring new ideas, we deliver cutting-edge solutions, staying ahead in our industry."
              icon={<Compass />}
              text="Innovation"
            />
            <ValueCard
              desc="Our customers are paramount. Attentively listening to their needs, we provide exceptional experiences, nurturing loyalty."
              icon={<UserGroup />}
              text="Customer Focus"
            />
          </Box>
        </Box>
        <Box className="w-full md:w-8/12 flex flex-col justify-center items-start py-5 gap-5">
          <Link href={"/contact"} className="w-full group">
            <Button
              className={
                "py-6 focus:outline-none bg-bg-secondary font-manrope font-medium text-sm w-full group-hover:text-[#2766fe] text-inherit hover:outline-none border-none :hover:border-none hover:shadow-md hover:shadow-neutral-800 duration-300 transition-all" +
                styles.about_get_in_touch
              }
              variant="outlined"
              sx={{
                width: "100%",
                border: "1px solid #2a2a2a",
                borderRadius: "11px",
              }}
              startIcon={<Chat />}
            >
              Get In Touch
            </Button>
          </Link>
        </Box>
      </PageLayout>
    </main>
  );
}

export default About;
