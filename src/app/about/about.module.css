.connect_github_button {
  border-radius: 16px;
  border: 1px solid #2a2a2a;
  background: #1f1f1f;
  transition: 0.3s all ease-in-out;
}

.connect_github_button:hover {
  background: #2f2f2f;
}

.about_header {
  color: #CACACA;
  text-align: center;
  font-size: 29px;
  font-style: normal;
  font-weight: 500;
  line-height: 145%; /* 42.05px */
  width: 698.574px;
}

.about_desc {
  color: #9c9c9c;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 170%; /* 25.6px */
  width: 698.574px;
}

.button {
  border: none;
  color: #f5f5f5;
  cursor: pointer;
  outline: none;
  font-size: 16px;
  border-radius: 25px;
  background-image: linear-gradient(to right, #2758d1 15%, #172285 45%);
  background-size: 200% 100%;
  transition: background-position 1s;
  z-index: 1000;
  background-position: 0 0;
  font-weight: 500;
}

.button:hover {
  background-position: 100% 250%;
}

.about_text {
  color: #8b8787;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%; /* 28.8px */
  align-self: stretch;
  padding-top: 10px;
  text-align: justify;
}

.about_title {
  color: #cacaca;
  text-align: center;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 145%; /* 29px */
  padding-top: 30px;
}

.about_value {
  color: #cacaca;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%; /* 32px */
}

.about_value_text {
  color: #c9c9c9;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.about_value_desc {
  color: #b5b5b5;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 160%; /* 25.6px */
}

.about_get_in_touch {
  display: flex;
  width: 1000px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 25px;
}

@media (max-width: 767px) {
  .button {
    padding: 5px 15px;
    font-size: 13px;
  }
  .about_header {
    font-size: 21px;
    width: 95%;
  }

  .about_desc {
    width: 95%;
    font-size: 14px;
  }
  .about_title{
    font-size: 16px;
  }
  .about_text{
    font-size: 14px;
    text-align: left;
    line-height: 165%;
  }
  .about_value_text{
    font-size: 16px;
  }
  .about_value_desc{
    font-size: 14px;
  }
}
