import { Button, Typography } from "@mui/material";
import React from "react";
import styles from "../about.module.css";
import Link from "next/link";

function Hero() {
  return (
    <>
      {/* <Button className="gap-3 py-1.5 px-4 text-tx-primary font-manrope tracking-wide font-medium bg-bg-secondary text-sm "
        sx={{
          border: "1px solid #1F1F1F",
        }}
        variant="outlined"
        color="inherit"
      >
        About Us
      </Button> */}

      <h1 className={"font-manrope " + styles.about_header}>
        Kuberns is the only AI Cloud PaaS provider; that revolutionizes cloud deployment for Startups, IT services, Enterprises, DevOps Teams and freelancers.
      </h1>

      <h3 className={"font-manrope " + styles.about_desc}>
        Our mission is to make deployment with AI cloud automation by eliminating infrastructure burdens, reducing manual work and freeing developers to focus on building and scaling without limitations.
      </h3>
      <Link href={"/contact"}>
        <Button variant="contained" size={"medium"} className={"font-manrope px-6 py-6 " + styles.button}>
          Contact Us
        </Button>
      </Link>
    </>
  );
}

export default Hero;
