import { ABOUT_US } from "@/constants/vars";
import { Box, Typography } from "@mui/material";
import Image from "next/image";
import styles from "../about.module.css";

function DetailsComp() {
  return (
    <Box className="w-full md:w-8/12 flex flex-col justify-center items-start">
      <Image
        src={ABOUT_US}
        alt="about"
        width={1200}
        height={500}
        className="pb-8 md:pb-16"
        title="Deploy your backend in one click with Kuberns"
      />
      <Typography className={"font-manrope " + styles.about_text}>
        Welcome to{" "}
        <span className="font-manrope text-slate-100 font-semibold">
          Kuberns
        </span>
        , where we're on a mission to revolutionize cloud deployment through our
        cutting-edge Platform as a Service (PaaS) offering. As a dynamic and
        innovative startup, our team is dedicated to simplifying the
        complexities of cloud technologies, enabling you to focus on what truly
        matters—your code.
      </Typography>
      <h2 className={"font-manrope " + styles.about_title}>
        The Problem: Manual Deployments Cause Delays and Costs
      </h2>
      <Typography className={"font-manrope " + styles.about_text}>
        Let’s be honest! We all know how developers and DevOps teams spend hours managing infrastructure, troubleshooting issues, and handling repetitive tasks that delay releases and increase costs for companies. {" "}
      </Typography>
      <h2 className={"font-manrope " + styles.about_title}>
      Here is our AI-Powered Solution
      </h2>
      <Typography className={"font-manrope " + styles.about_text}>
        At Kuberns, we are redefining DevOps automation by streamlining deployments, eliminating manual intervention, and reducing errors with AI. But automation is just the start. Our platform offers auto-deployment with real-time server monitoring, detailed metrics and logs, build history, and seamless custom domain integration.
      </Typography>
      <Typography className={"font-manrope " + styles.about_text}>
        With deployment automation across all geo-regions, we enable businesses to deploy applications instantly, whether in a single location or multiple regions. And the best part? Our AI cloud deployment solution is cost-effective, ensuring maximum efficiency at an affordable price.
      </Typography>
      <h2 className={"font-manrope " + styles.about_title}>
        What We Believe
      </h2>
      <Typography className={"font-manrope " + styles.about_text}>
        We know developers are best at coding, but manual deployment slows them down and takes away their focus. Instead of spending time on innovation, they get stuck managing infrastructure, fixing errors, and handling repetitive tasks.
      </Typography>
      <Typography className={"font-manrope " + styles.about_text}>
        That’s why we built Kuberns - With cloud AI we automate DevOps workflows, streamline automated software deployment and let developers do what they do best: write great code.
      </Typography>
      <Typography className={"font-manrope " + styles.about_text}>
        Transparency is at the heart of everything we do. we believe in being open and honest with our users. No hidden steps, no complicated setups - just a simple, transparent system that works.
      </Typography>
      <Typography className={"font-manrope " + styles.about_text}>
        Join us at Kuberns and experience a seamless, efficient, and transparent auto deployment journey. Let us handle the complexities so you can innovate without limits.
      </Typography>
    </Box>
  );
}

export default DetailsComp;
