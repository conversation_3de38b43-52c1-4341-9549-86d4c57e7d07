import { Box, Typography } from "@mui/material";
import React from "react";
import styles from "../about.module.css";

function ValueCard({
  icon,
  text,
  desc,
}: {
  icon: React.ReactNode;
  text: string;
  desc: string;
}) {
  return (
    <Box className="flex w-full flex-col justify-start items-start gap-4 group cursor-auto hover:shadow-lg hover:shadow-neutral-800 duration-300 transition-all p-5 rounded-xl">
      <Box className="flex w-full justify-start items-center gap-4">
        <Box className="bg-[#212121] size-8 md:size-10 rounded-lg flex justify-center items-center group-hover:bg-[#2766fe] duration-300 transition-all">
          {icon}
        </Box>
        <h3 className={"font-manrope group-hover:text-[#2766fe] duration-300 transition-all " + styles.about_value_text}>{text}</h3>
      </Box>
      <Typography className={"font-manrope " + styles.about_value_desc}>{desc}</Typography>
    </Box>
  );
}

export default ValueCard;
