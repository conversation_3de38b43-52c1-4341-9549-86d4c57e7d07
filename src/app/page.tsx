"use client";

import PageLayout from "@/components/Layout";
import { Box } from "@mui/material";
import { Manrope } from "next/font/google";
import ConnectGithub from "./HomeComponent/ConnectGithub";
import Features from "./HomeComponent/Features";
import HeroHome from "./HomeComponent/Hero";
import Plans from "./HomeComponent/Plans";
import Services from "./HomeComponent/Services";
import ServiceSpanGlobe from "./HomeComponent/ServiceSpanGlobe";
import Testimonials from "./HomeComponent/Testimonials";
import Video from "./HomeComponent/IntroVideo";
import WhyKuberns from "./HomeComponent/WhyKuberns";
import styles from "./page.module.css";
import { useEffect, useState } from "react";
import { useAppDispatch } from "@/redux/store";
import { openToast } from "@/redux/slices/toastSlice";
import { getToastMsgs } from "@/redux/slices/appSlice";
import { setIsMarketingPopupActive } from "@/redux/slices/rootSlice";
import MarketingPopup from "@/components/MarketingPopups";

export default function Home() {
  const dispatch = useAppDispatch();
  const [isInteracted, setIsInteracted] = useState(false);

  const fetchToastMessage = async () => {
    await dispatch(getToastMsgs());
    setTimeout(() => {
      dispatch(openToast());
    }, 2000);
  };

  useEffect(() => {
    const enableContentLoad = () => {
      setIsInteracted(true);
      document.removeEventListener("scroll", enableContentLoad);
      document.removeEventListener("mousemove", enableContentLoad);
      document.removeEventListener("keydown", enableContentLoad);
    };

    document.addEventListener("scroll", enableContentLoad);
    document.addEventListener("mousemove", enableContentLoad);
    document.addEventListener("keydown", enableContentLoad);

    return () => {
      document.removeEventListener("scroll", enableContentLoad);
      document.removeEventListener("mousemove", enableContentLoad);
      document.removeEventListener("keydown", enableContentLoad);
    };
  }, []);

  useEffect(() => {
    fetchToastMessage();
  }, []);

  useEffect(() => {
    setTimeout(() => {
      dispatch(setIsMarketingPopupActive(true));
    }, 15000);
  }, []);

  useEffect(() => {
    // Inject the Facebook Pixel script
    const script = document.createElement("script");
    script.innerHTML = `
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '1800301930731747');
      fbq('track', 'PageView');
    `;
    script.async = true;
    document.head.appendChild(script);

    // Inject the noscript tag
    const noscript = document.createElement("noscript");
    noscript.innerHTML = `<img height="1" width="1" style="display:none" 
      src="https://www.facebook.com/tr?id=1800301930731747&ev=PageView&noscript=1" />`;
    document.body.appendChild(noscript);

    // Cleanup on unmount
    return () => {
      document.head.removeChild(script);
      document.body.removeChild(noscript);
    };
  }, []);

  return (
    <main className={`flex flex-col items-center font-manrope  `}>
      <link rel="canonical" href="https://kuberns.com" />
      {/* <MarketingPopup /> */}

      <PageLayout
        className={`w-full flex justify-center items-center flex-col z-10`}
      >
        <HeroHome />

        {/* {isInteracted && (
          <>
            <Video />
            <Box className="h-10 md:h-24" />
            <WhyKuberns />
            <Box className="h-10 md:h-40" />
            <Services />
            <Features />
            <ServiceSpanGlobe />
            <Box className="h-20 md:h-40" />
            <Plans />
            <Box className="md:h-10" />
          </>
        )} */}
      </PageLayout>
      {/* {isInteracted && (
        <>
          <Testimonials />
          <PageLayout className="w-full flex justify-center items-center pt-10 md:pt-20 xl:pt-32 xl:pb-8">
            <ConnectGithub />
          </PageLayout>
        </>
      )} */}
    </main>
  );
}
