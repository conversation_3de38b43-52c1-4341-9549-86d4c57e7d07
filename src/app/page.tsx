import PageLayout from "@/components/Layout";
import MarketingPopup from "@/components/MarketingPopups";
import { HERO_DEFAULTS } from "@/constants/defaults/hero";
import HeroHome from "@/modules/home/<USER>";
import dynamic from "next/dynamic";

const IntroVideo = dynamic(() => import("@/modules/home/<USER>"));

export default function Home() {
  return (
    <PageLayout
      className={`w-full flex justify-center items-center flex-col font-manrope`}
    >
      <link rel="canonical" href="https://kuberns.com" />
      <MarketingPopup />
      <HeroHome content={HERO_DEFAULTS} />
      <IntroVideo />
      {/* {isInteracted && (
          <>
            <Video />
            <Box className="h-10 md:h-24" />
            <WhyKuberns />
            <Box className="h-10 md:h-40" />
            <Services />
            <Features />
            <ServiceSpanGlobe />
            <Box className="h-20 md:h-40" />
            <Plans />
            <Box className="md:h-10" />
          </>
        )} */}
      {/* {isInteracted && (
        <>
          <Testimonials />
          <PageLayout className="w-full flex justify-center items-center pt-10 md:pt-20 xl:pt-32 xl:pb-8">
            <ConnectGithub />
          </PageLayout>
        </>
      )} */}
    </PageLayout>
  );
}
