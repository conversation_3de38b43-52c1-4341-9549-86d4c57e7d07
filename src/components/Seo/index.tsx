"use client";
import Head from "next/head";
import { usePathname } from "next/navigation";
import { LOGO } from "@/constants/vars";

export default function Seo(title: any, description: any) {
    const baseUrl = 'https://kuberns.com';
    const pathname = usePathname();
    const url = (baseUrl + pathname).split('?')[0]

    return (
        <Head>
            {/* Basic SEO */}
            <title>{title}</title>
            <meta name="description" content={description} />

            {/* Open Graph (Facebook, LinkedIn, Instagram) */}
            <meta property="og:title" content={title} />
            <meta property="og:description" content={description} />
            <meta property="og:image" content={LOGO} />
            <meta property="og:url" content={baseUrl} />
            <meta property="og:type" content="website" />
            <meta property="og:site_name" content="Kuberns" />

            {/* Twitter Meta Tags */}
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" content={title} />
            <meta name="twitter:description" content={description} />
            <meta name="twitter:image" content={LOGO} />
            <meta name="twitter:url" content={baseUrl} />
            <meta name="twitter:site" content="@Kuberns_cloud" />

            {/* Favicon */}
            <link rel="icon" href="/favicon.ico" />
            <link rel="canonical" href={url} />
        </Head>
    );
};

