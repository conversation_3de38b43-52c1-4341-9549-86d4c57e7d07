import useMediaQuery from "@/hooks/useMediaQueryHook";
import { useAppSelector } from "@/redux/store";
import { formatStorageSize } from "@/utils";
import { Box, Typography } from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import React from "react";

interface Props {
  rows: any[];
  sx?: any;
  isLastRowBlur?: boolean;
  autoHeight?: boolean;
  type: "SERVER" | "DATABASE";
  isUsd?: boolean;
}

function PlanDataGrid(props: Props) {
  const {
    rows,
    sx,
    isLastRowBlur,
    autoHeight = false,
    type,
    isUsd = true,
  } = props;

  const isTablet = useMediaQuery("(max-width: 1023px)");

  const { usdToInrConversion } = useAppSelector((state) => state.pricingSlice);

  const getDiscountedPrice = (price: number) => {
    if (type === "SERVER") return Number(price * 0.7);

    if (type === "DATABASE") return Number(price * 0.9);

    return price;
  };

  const getUsdToInrPrice = (price: number) => {
    if (isUsd) return price;

    return price * usdToInrConversion;
  };

  const getUsdOrInrSymbol = () => {
    if (isUsd) return "$";
    return "₹";
  };

  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: "Compute Service",
      display: "flex",
      flex: 1,
      minWidth: 120,
      align: "left",
      headerAlign: "center",
      sortable: false,
      disableColumnMenu: true,
      renderHeader: () => {
        return (
          <Typography
            className={`text-sm font-semibold text-[#A0A0A0] tracking-wide`}
          >
            COMPUTE SERVICE
          </Typography>
        );
      },
    },
    {
      field: "default_ram",
      headerName: "Memory",
      display: "flex",
      headerAlign: "center",
      flex: 1,
      minWidth: 120,
      align: "center",
      sortable: false,
      resizable: false,
      disableColumnMenu: true,
      renderHeader: () => {
        return (
          <Typography
            className={`text-sm font-semibold text-[#A0A0A0] tracking-wide`}
          >
            MEMORY
          </Typography>
        );
      },
      renderCell(params) {
        return (
          <Box className="w-full h-full flex justify-center items-center">
            {formatStorageSize(params.row.default_ram)}
          </Box>
        );
      },
    },
    {
      field: "default_vcpu",
      headerName: "vCPUs",
      display: "flex",
      flex: 1,
      minWidth: 120,
      headerAlign: "center",
      align: "center",
      sortable: false,
      resizable: false,
      disableColumnMenu: true,
      renderHeader: () => {
        return (
          <Typography
            className={`text-sm  font-semibold text-[#A0A0A0] tracking-wide`}
          >
            VCPUS
          </Typography>
        );
      },
      renderCell(params) {
        return (
          <Box className="w-full h-full flex justify-center items-center">
            {params.row.default_vcpu}
          </Box>
        );
      },
    },
    {
      field: "compute_cost_per_hour",
      headerName: "Price_per_hour",
      display: "flex",
      flex: 1,
      minWidth: 180,
      headerAlign: "center",
      align: "center",
      sortable: false,
      disableColumnMenu: true,
      resizable: false,
      renderHeader: () => {
        return (
          <Typography
            className={`text-sm font-semibold text-[#A0A0A0] tracking-wide`}
          >
            PRICE PER HOUR
          </Typography>
        );
      },
      renderCell(params) {
        return (
          <Box className="w-full h-full flex justify-center items-center gap-2">
            <p className="line-through">
              {getUsdOrInrSymbol()}
              {getUsdToInrPrice(params.row.compute_cost_per_hour).toFixed(2)}
            </p>
            <p>
              {getUsdOrInrSymbol()}
              {getDiscountedPrice(
                getUsdToInrPrice(params.row.compute_cost_per_hour)
              ).toFixed(2)}
            </p>
          </Box>
        );
      },
    },
    {
      field: "compute_cost_per_month",
      headerName: "Price_per_month",
      display: "flex",
      flex: 1,
      minWidth: 180,
      headerAlign: "center",
      align: "center",
      sortable: false,
      disableColumnMenu: true,
      resizable: false,
      renderHeader: () => {
        return (
          <Typography
            className={`text-sm font-semibold text-[#A0A0A0] tracking-wide`}
          >
            PRICE PER MONTH
          </Typography>
        );
      },
      renderCell(params) {
        return (
          <Box className="w-full h-full flex justify-center items-center gap-2">
            <p className="line-through">
              {getUsdOrInrSymbol()}
              {getUsdToInrPrice(
                params.row.compute_cost_per_hour * 24 * 30
              ).toFixed(2)}
            </p>
            <p>
              {getUsdOrInrSymbol()}
              {(
                Number(getDiscountedPrice(getUsdToInrPrice(params.row.compute_cost_per_hour))) *
                24 *
                30
              ).toFixed(0)}
            </p>
          </Box>
        );
      },
    },
  ];

  return (
    <DataGrid
      sx={{
        overflow: "auto",
        borderTopLeftRadius: "18px", // Rounded corners for the DataGrid
        borderTopRightRadius: "18px", // Rounded corners for the DataGrid
        border: "none",

        "& .header .MuiDataGrid-cell--withBorder": {
          border: "none !important",
        },
        "& .MuiDataGrid-columnHeader:focus-within": {
          outline: "none !important",
        },

        "& .header": {
          fontSize: "16px",
          height: "50px",
          fontWeight: "600",
          borderBottom: "none !important",
          borderTop: "none",
        },
        "& .MuiDataGrid-row": {
          fontSize: isTablet ? "14px" : "16px",
          margin: "3px 0", // Adds vertical spacing between rows
          border: "none",
          background: "#1f1f1f",
          borderTop: "none",
          borderRadius: "8px", // Rounded corners for each row
          "--rowBorderColor": "transparent",
          fontWeight: "600",
        },
        "& .MuiDataGrid-row:hover": {
          backgroundColor: "#222222",
        },
        "& .MuiDataGrid-cell:focus-within": {
          outline: "none !important",
        },
        "& .MuiDataGrid-cell--withBorder": {
          border: "none !important",
        },
        "& .MuiDataGrid-columnSeparator": {
          display: "none",
        },
        "& .MuiDataGrid-columnHeaders": {
          border: "none !important",
        },
        "& .MuiDataGrid-columnHeader": {
          border: "3px solid #303030",
          borderTop: 0,
          borderLeft: 0,
          borderBottom: 0,
          marginTop: "10px",
          marginBottom: "10px",
          maxHeight: "35px",
        },
        "& .MuiDataGrid-columnHeader--last": {
          border: "none !important",
        },
        "& .MuiDataGrid-cell": {
          border: "3px solid #303030",
          padding: "0px 16px",
          fontWeight: "400",
          color: "#cacaca",
          borderTop: 0,
          borderLeft: 0,
          borderBottom: 0,
        },
        "& .MuiDataGrid-cell:last-child": {
          border: "none !important",
        },
        "& .last-row-shadow": {
          boxShadow: "inset 0px -20px 30px -1px rgba(0,0,0,0.75)",
          opacity: "0.4",
        },
        "& .MuiDataGrid-virtualScroller": {
          scrollbarWidth: "thin",
          overflowY: "hidden",
          "&::-webkit-scrollbar": {
            display: "none", // Hide horizontal scrollbar
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#858585", // Scrollbar thumb color
            borderRadius: "10px",
          },
          "&::-webkit-scrollbar-track": {
            background: "#1C1C1C", // Scrollbar track color
          },
        },
        "& .MuiDataGrid-scrollbar--horizontal": {
          display: "none", // Hide horizontal scrollbar
        },
        // Scroll effect for horizontal scrolling without visible scrollbar
        "& .MuiDataGrid-virtualScrollerContent": {
          overflowX: "auto", // Enable horizontal scrolling
        },
        ...sx,
      }}
      rows={rows}
      columns={columns}
      hideFooterPagination
      checkboxSelection={false}
      autoHeight={autoHeight}
      getRowClassName={(params) => {
        return params.id === rows[rows.length - 1].public_id && isLastRowBlur
          ? "last-row-shadow"
          : "";
      }}
      hideFooter
      density="standard"
      getRowId={(row) => row.public_id}
    />
  );
}

export default PlanDataGrid;
