"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { Box, Typography } from "@mui/material";
import { DASHBOARD_LINK, STAR_ICON } from "@/constants/vars";
import { useAppSelector } from "@/redux/store";
import Image from "next/image";

const TopBar = () => {
  const { isHomePage } = useAppSelector((state) => state.appSlice);

  return (
    <>
      {isHomePage && (
        <Box
          className="hidden lg:flex item-center justify-center w-full text-white py-[10px] mb-1 h-[39px]"
          style={{
            background:
              "linear-gradient(90deg, #169228 0%, #0F651C 43.9%, #072C0C 100%)",
          }}
        >
          <Image
            className="mr-[40px]"
            src={STAR_ICON}
            width={18}
            height={18}
            alt="top bar star icon"
          />
          <Typography
            className="font-manrope"
            fontSize="14px"
            lineHeight={"normal"}
          >
            Zero platform fees for lifetime, pay only for compute!{" "}
            <Link
              href={DASHBOARD_LINK}
              target="_blank"
              className="font-medium underline cursor-pointer"
            >
              Join Now
            </Link>
          </Typography>
          <Image
            className="ml-[40px]"
            src={STAR_ICON}
            width={18}
            height={18}
            alt="top bar star icon"
          />
        </Box>
      )}
      {isHomePage && (
        <Box
          className="flex lg:hidden item-center justify-center w-full text-white py-[10px] mb-8 h-[39px] min-w-0"
          style={{
            whiteSpace: "nowrap", // Ensure text stays in a single line
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: "100%",
            background:
              "linear-gradient(90deg, #169228 0%, #0F651C 43.9%, #072C0C 100%)",
          }}
        >
          <Typography
            className="font-manrope text-[3.5vw] sm:text-[14px] leading-tight"
            fontSize={"14px"}
          >
            Zero platform fees for lifetime, pay only for compute!{" "}
            <Link
              href={DASHBOARD_LINK}
              target="_blank"
              className="font-medium underline cursor-pointer"
            >
              Join Now
            </Link>
          </Typography>
        </Box>
      )}
    </>
  );
};

export default TopBar;
