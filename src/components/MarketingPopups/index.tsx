import { setIsMarketingPopupActive } from "@/redux/slices/rootSlice";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { Box, IconButton, Modal } from "@mui/material";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { IoMdClose } from "react-icons/io";
import CompanyLeader from "./components/CompanyLeader";
import JustExploring from "./components/JustExploring";
import RootContent from "./components/RootContent";
import SoloDeveloper from "./components/SoloDeveloper";
import EmailInput from "./components/EmailInput";

function MarketingPopup() {
  const dispatch = useAppDispatch();

  const fadeInVariant = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.5 } },
  };

  const { isMarketingPopupActive } = useAppSelector((state) => state.root);

  const handleClose = () => {
    dispatch(setIsMarketingPopupActive(false));
  };

  const [showCloseBtn, setShowCloseBtn] = useState(false);

  const [selectedType, setSelectedType] = useState("");

  const [showFinalContent, setShowFinalContent] = useState(false);

  const handleClick = (x: string) => setSelectedType(x);

  useEffect(() => {
    if (isMarketingPopupActive) {
      setTimeout(() => {
        setShowCloseBtn(true);
      }, 2000);
    }
  }, [isMarketingPopupActive]);

  return (
    <Modal
      open={isMarketingPopupActive}
      onClose={handleClose}
      closeAfterTransition
      disableEscapeKeyDown
      disableAutoFocus
    >
      <Box
        className={`absolute top-[50%] md:top-[45%] left-[50%] w-[90%] sm:w-[450px] h-auto border-dashed md:border-none border-2 border-[#232323] shadow-xl z-50 translate-x-[-50%] translate-y-[-50%] bg-[#000] rounded-3xl noiseBgImage overflow-hidden gap-5 font-manrope flex flex-col`}
      >
        <Box className="absolute top-4 right-4 z-50">
          {showCloseBtn && !showFinalContent && (
            <motion.div
              variants={fadeInVariant}
              initial="hidden"
              animate="visible"
            >
              <IconButton
                onClick={() => dispatch(setIsMarketingPopupActive(false))}
              >
                <IoMdClose color={"#858585"} />
              </IconButton>
            </motion.div>
          )}
        </Box>
        {!selectedType && !showFinalContent && (
          <RootContent handleClick={handleClick} handleClose={handleClose} />
        )}
        {selectedType === "1" && !showFinalContent && (
          <SoloDeveloper secondaryAction={() => setShowFinalContent(true)} />
        )}
        {selectedType === "2" && !showFinalContent && (
          <CompanyLeader secondaryAction={() => setShowFinalContent(true)} />
        )}
        {selectedType === "3" && !showFinalContent && (
          <JustExploring secondaryAction={() => setShowFinalContent(true)} />
        )}
        {/* {showFinalContent && <EmailInput handleClose={handleClose} />} */}
      </Box>
    </Modal>
  );
}

export default MarketingPopup;
