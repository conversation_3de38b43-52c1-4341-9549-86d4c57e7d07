import HighlightWords from "@/app/HomeComponent/WordHighlight";
import { MarketingUserTypes, SoloDeveloperTypes } from "@/constants/Marketing";
import { CIPY_GEEK_1, GOOGLE_LINK } from "@/constants/vars";
import { Box, Divider, Typography } from "@mui/material";
import { motion } from "framer-motion";
import Image from "next/image";
import PrimaryButton from "./PrimaryButton";
import SecondaryButton from "./SecondaryButton";

function SoloDeveloper({ secondaryAction }: { secondaryAction: () => void }) {
  const slideInVariant = {
    hidden: { x: "100%", opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { duration: 0.2, ease: "easeOut" },
    },
  };

  return (
    <motion.div variants={slideInVariant} initial="hidden" animate="visible">
      <Box className="p-5">
        <Box className="flex items-center justify-start w-full">
          <Image
            src={CIPY_GEEK_1}
            width={80}
            height={80}
            alt="cipygeek1"
            title="Deploy your backend in one click with Kuberns"
          />
          <Box className="flex w-full items-center pl-5 md:pr-5">
            <Typography className="w-[35px] text-xs md:text-sm">
              I am
            </Typography>
            <Box
              className="w-auto flex items-center justify-start text-xs md:text-sm gap-2 border border-dashed border-st-light px-3 py-2 rounded-lg cursor-pointer hover:border-br-primary duration-200 transition-all text-tx-primary"
              sx={{
                background: "linear-gradient(90deg, #191919 0%, #111 100%)",
                backdropFilter: "blur(6.074999809265137px)",
              }}
            >
              {MarketingUserTypes[0].icon} {MarketingUserTypes[0].title}
            </Box>
          </Box>
        </Box>
        <Box className="w-full flex flex-col items-start justify-center">
          <Typography className="gradient_text text-left text-base md:text-lg">
            That’s Great! We know what it means to
          </Typography>
          <Typography className="gradient_text text-left text-base md:text-lg">
            be a Solo Developer.
          </Typography>
        </Box>
        <Box className="w-full flex items-center justify-center pt-2 pb-5">
          <Typography className="text-tx-secondary text-xs md:text-sm font-semibold">
            And that’s why we at Kuberns try to help as many solo Developer as
            we can with our
          </Typography>
        </Box>
        <Divider className="border border-dashed border-st-light" />
        <Box className="flex flex-col items-center justify-center w-full gap-3 py-5">
          {SoloDeveloperTypes.map((item) => {
            return (
              <Box
                className="w-full flex items-center text-sm md:text-base justify-start gap-2 border border-dashed border-st-light px-3 py-2 rounded-lg cursor-pointer hover:border-br-primary duration-200 transition-all text-tx-primary"
                key={item.id}
                sx={{
                  background: "linear-gradient(90deg, #191919 0%, #111 100%)",
                  backdropFilter: "blur(6.074999809265137px)",
                }}
              >
                {item.icon} {item.title}
              </Box>
            );
          })}
        </Box>
        <HighlightWords
          text="Eager to kickstart your journey? Don't miss our free trial worth 500 Kredits! Claim it now and let's get the ball rolling."
          wordsToHighlight={["500 Kredits"]}
          className="text-tx-secondary text-xs md:text-sm tracking-wide font-manrope font-semibold"
          highlightClassName="text-tx-primary"
        />
        <Box className="flex w-full flex-col md:flex-row items-center gap-3 pt-5 pb-2">
          <PrimaryButton
            primaryLink={GOOGLE_LINK}
            text="Let me give it a try"
          />
          <SecondaryButton
            size="medium"
            text="I'll miss out!"
            action={secondaryAction}
          />
        </Box>
        <Box className="flex items-center justify-start gap-0.5">
          <span className="text-red-600 text-sm">*</span>
          <span className="text-tx-primary text-xs">
            It's only available for a limited time!
          </span>
        </Box>
      </Box>
    </motion.div>
  );
}

export default SoloDeveloper;
