import useMediaQuery from "@/hooks/useMediaQueryHook";
import { setIsMarketingPopupActive } from "@/redux/slices/rootSlice";
import { useAppDispatch } from "@/redux/store";
import { Button } from "@mui/material";
import Link from "next/link";

function SecondaryButton({
  size,
  primaryLink,
  text,
  action,
}: {
  size: "small" | "medium";
  text: string;
  primaryLink?: string;
  action?: () => void;
}) {
  const isMobile = useMediaQuery("(max-width: 1023px)");

  const dispatch = useAppDispatch();

  let style =
    size === "small" ? { padding: "5px 15px" } : { padding: "8px 18px" };

  let btn = (
    <Button
      variant="contained"
      size={isMobile ? "small" : "medium"}
      className={`rounded-xl text-sm w-full bg-[#111] border border-solid border-st-light`}
      color="inherit"
      sx={{
        ...style,
        color: "#fff",
        cursor: "pointer",
      }}
      onClick={action}
    >
      {text}
    </Button>
  );

  if (action) {
    return btn;
  }

  if (primaryLink) {
    return (
      <Link
        href={primaryLink}
        passHref={true}
        target="_blank"
        className="w-full"
        onClick={() => {
          window.open(primaryLink, "_blank");
          dispatch(setIsMarketingPopupActive(false));
        }}
        title="Get Started"
      >
        {btn}
      </Link>
    );
  }
}

export default SecondaryButton;
