import {
  GITHUB_AUTH_LINK,
  GITHUB_ICON,
  GOOGLE_AUTH_LINK,
  GOOGLE_ICON,
} from "@/constants/vars";
import { Box, Button, Divider, Typography } from "@mui/material";
import Image from "next/image";

function RootContent({
  handleClick,
  handleClose,
}: {
  handleClick: (x: string) => void;
  handleClose: () => void;
}) {
  const handleGithubLink = () => {
    window.open(GITHUB_AUTH_LINK, "_blank");
  };
  const handleGoogleLink = () => {
    window.open(GOOGLE_AUTH_LINK, "_blank");
  };
  return (
    <Box className="w-full flex flex-col items-center justify-center gap-[25px] p-[40px]">
      <Box className="w-full flex flex-col items-center justify-center gap-[25px]">
        <Box>
          <Typography
            className="gradient_text text-start font-manrope"
            fontSize={"28px"}
            lineHeight={"42px"}
            fontWeight={400}
          >
            Zero-config, lightning-fast deployment! Focus on building, we handle
            the rest.
          </Typography>
        </Box>
        <Box>
          <Typography
            className="font-manrope"
            lineHeight={"27.2px"}
            fontSize={"16px"}
            fontWeight={400}
          >
            Sign up today and{" "}
            <span
              style={{
                background:
                  " linear-gradient(60deg, #0538FF 13.4%, #6B57F5 86.6%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                display: "inline-block",
              }}
            >
              Zero platform fees
            </span>{" "}
            for lifetime, pay only for compute
          </Typography>
        </Box>
      </Box>
      <Divider className="border border-st-light w-[402px]" />
      <Box className="flex flex-col items-center justify-center w-full gap-3">
        <Button
          className="flex items-center justify-start w-[291px] p-3 py-5 bg-[#0081AF] gap-10 cursor-pointer rounded-[12px]"
          onClick={handleGoogleLink}
        >
          <Box className={"p-1 bg-[white]"} borderRadius={"8px"}>
            <Image
              src={GOOGLE_ICON}
              width={22.5}
              height={22.5}
              alt={"google"}
            />
          </Box>
          <Typography
            className="font-manrope"
            color={"white"}
            fontWeight={500}
            fontSize={"15px"}
          >
            Continue With Google
          </Typography>
        </Button>
        <Button
          className="flex items-center justify-start w-[291px] p-3 py-5 bg-[white] gap-10 cursor-pointer rounded-[12px]"
          onClick={handleGithubLink}
        >
          <Box className={"p-1 bg-[white]"} borderRadius={"8px"}>
            <Image
              src={GITHUB_ICON}
              width={22.5}
              height={22.5}
              alt={"github"}
            />
          </Box>
          <Typography
            className="font-manrope"
            color={"black"}
            fontWeight={500}
            fontSize={"15px"}
          >
            Continue With Github
          </Typography>
        </Button>
        {/* <Button onClick={handleClose}>
          <Typography
            fontSize={"12px"}
            fontWeight={400}
            lineHeight={"21px"}
            color={"white"}
            className="font-manrope"
          >
            I'll miss out
          </Typography>
        </Button> */}
      </Box>
    </Box>
  );
}

export default RootContent;
