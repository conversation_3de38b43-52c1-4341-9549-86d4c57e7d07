import HighlightWords from "@/app/HomeComponent/WordHighlight";
import { CompanyLeaderTypes, MarketingUserTypes } from "@/constants/Marketing";
import { CALENDLY_LINK, CIPY_GEEK_1, DASHBOARD_LINK } from "@/constants/vars";
import { Box, Divider, Typography } from "@mui/material";
import { motion } from "framer-motion";
import Image from "next/image";
import PrimaryButton from "./PrimaryButton";
import SecondaryButton from "./SecondaryButton";

function CompanyLeader({ secondaryAction }: { secondaryAction: () => void }) {
  const slideInVariant = {
    hidden: { x: "100%", opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: { duration: 0.2, ease: "easeOut" },
    },
  };

  return (
    <motion.div variants={slideInVariant} initial="hidden" animate="visible">
      <Box className="p-5">
        <Box className="flex items-center justify-start w-full">
          <Image src={CIPY_GEEK_1} width={80} height={80} alt="cipygeek1" title="Deploy your backend in one click with Kuberns" />
          <Box className="flex w-full items-center px-5">
            <Typography className="w-[35px] text-xs md:text-sm">
              I am
            </Typography>
            <Box
              className="w-auto flex items-center justify-start gap-2 text-xs md:text-sm border border-dashed border-st-light px-3 py-2 rounded-lg cursor-pointer hover:border-br-primary duration-200 transition-all text-tx-primary"
              sx={{
                background: "linear-gradient(90deg, #191919 0%, #111 100%)",
                backdropFilter: "blur(6.074999809265137px)",
              }}
            >
              {MarketingUserTypes[1].icon} {MarketingUserTypes[1].title}
            </Box>
          </Box>
        </Box>
        <Box className="w-full flex flex-col items-start justify-center">
          <Typography className="gradient_text text-left text-base md:text-lg">
            That’s great! We understand the challenges of leading a team or
            organization.
          </Typography>
          <Typography className="gradient_text text-left"></Typography>
        </Box>
        <Box className="w-full flex items-center justify-start pt-2 pb-5">
          <Typography className="text-tx-secondary text-xs md:text-sm font-semibold">
            At Kuberns, we aim to empower leaders like you by:
          </Typography>
        </Box>
        <Divider className="border border-dashed border-st-light" />
        <Box className="flex flex-col items-center justify-center w-full gap-3 py-5">
          {CompanyLeaderTypes.map((item) => {
            return (
              <Box
                className="w-full flex items-center justify-start text-sm md:text-base gap-2 border border-dashed border-st-light px-3 py-2 rounded-lg cursor-pointer hover:border-br-primary duration-200 transition-all text-tx-primary"
                key={item.id}
                sx={{
                  background: "linear-gradient(90deg, #191919 0%, #111 100%)",
                  backdropFilter: "blur(6.074999809265137px)",
                }}
              >
                {item.icon} {item.title}
              </Box>
            );
          })}
        </Box>
        <HighlightWords
          text="Book a 15-minute call with us to explore how Kuberns can accelerate your success. Let’s tailor our solutions to your needs!"
          wordsToHighlight={[""]}
          className="text-tx-secondary text-xs md:text-sm tracking-wide font-manrope font-semibold"
          highlightClassName="text-tx-primary"
        />
        <Box className="flex w-full flex-col md:flex-row items-center gap-3 pt-5 pb-2">
          <PrimaryButton primaryLink={CALENDLY_LINK} text="Book a Call Now!" />
          <SecondaryButton
            size="medium"
            text="Signup and Explore"
            primaryLink={DASHBOARD_LINK}
          />
        </Box>
        <Box
          className="flex items-center justify-center gap-0.5 cursor-pointer z-50 relative"
          onClick={secondaryAction}
        >
          <span className="text-xs text-tx-primary hover:text-tx-tertiary transition-all duration-200">
            I'll miss out!
          </span>
        </Box>
      </Box>
    </motion.div>
  );
}

export default CompanyLeader;
