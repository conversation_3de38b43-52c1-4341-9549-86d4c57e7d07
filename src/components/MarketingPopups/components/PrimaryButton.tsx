import { setIsMarketingPopupActive } from "@/redux/slices/rootSlice";
import { useAppDispatch } from "@/redux/store";
import { Button } from "@mui/material";
import Link from "next/link";
import styles from "../../nav/nav.module.css";

function PrimaryButton({
  primaryLink,
  text,
}: {
  text: string;
  primaryLink: string;
}) {
  const dispatch = useAppDispatch();

  return (
    <Link
      href={primaryLink}
      passHref={true}
      target="_blank"
      className="w-full"
      onClick={() => {
        window.open(primaryLink, "_blank");
        dispatch(setIsMarketingPopupActive(false));
      }}
      title="Get Started"
    >
      <Button
        variant="contained"
        size={"medium"}
        className={`${styles.button} rounded-xl text-sm w-full`}
        color="primary"
        sx={{ padding: "5px 15px" }}
      >
        {text}
      </Button>
    </Link>
  );
}

export default PrimaryButton;
