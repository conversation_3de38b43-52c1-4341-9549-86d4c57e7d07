import { CIPY_MAIL_1 } from "@/constants/vars";
import {
  saveUserEmail,
  setIsMarketingPopupActive,
} from "@/redux/slices/rootSlice";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import { Box, Divider, IconButton, Input, Typography } from "@mui/material";
import Image from "next/image";
import { useState } from "react";
import { IoIosMail } from "react-icons/io";
import { PiArrowBendDownRightBold } from "react-icons/pi";

function EmailInput({ handleClose }: { handleClose: () => void }) {
  const [value, setValue] = useState("");

  const dispatch = useAppDispatch();

  const { saveLoading } = useAppSelector((state) => state.root);

  const handleSave = async () => {
    const res = await dispatch(saveUserEmail({ email: value }));

    if (res.payload) {
      setTimeout(() => {
        dispatch(setIsMarketingPopupActive(false));
      }, 1000);
    }
  };

  return (
    <Box className="flex flex-col p-5">
      <Box className="flex items-center justify-center w-full">
        <Image
          src={CIPY_MAIL_1}
          width={100}
          height={100}
          alt="EmailInput"
          title="Deploy your backend in one click with Kuberns"
        />
      </Box>
      <Box className="w-full flex flex-col items-center justify-center">
        <Typography className="gradient_text text-center w-2/3 py-5">
          Take your time, We are just getting better everyday!
        </Typography>
      </Box>
      <Divider className="border border-dashed border-st-light" />
      <Box className="w-full flex items-center justify-center py-5">
        <Typography className="text-tx-secondary text-sm font-semibold">
          Let’s stay in touch, we’ll let you know about more features that might
          interest you in future!
        </Typography>
      </Box>
      <Box className="flex items-center justify-center w-full gap-3">
        <Box className="flex border border-dashed border-st-dark w-full rounded-lg items-center px-3 gap-3 py-1">
          <IoIosMail size={30} />
          <Input
            placeholder="Enter your email"
            className="w-full"
            disableUnderline
            onChange={(e) => setValue(e.target.value)}
            value={value}
            disabled={saveLoading}
          />
          <IconButton
            size="small"
            className="rounded-lg"
            onClick={handleSave}
            disabled={saveLoading}
          >
            <PiArrowBendDownRightBold size={20} />
          </IconButton>
        </Box>
      </Box>
      <Box className="w-full flex items-center justify-center py-5">
        <Typography className="text-tx-secondary text-sm font-semibold">
          Don’t worry we won’t spam your Inbox!
        </Typography>
      </Box>
      <Box
        className="w-full flex flex-col items-center justify-center "
        onClick={handleClose}
      >
        <Typography className="gradient_text text-center w-2/3 py-5 text-base cursor-pointer z-50">
          Continue browsing!
        </Typography>
      </Box>
    </Box>
  );
}

export default EmailInput;
