"use client";

import Popover from "@mui/material/Popover";
import { ReactNode } from "react";

interface Props {
  anchorEl: any;
  open: any;
  setAnchorEl: any;
  component: ReactNode;
  handleClose: () => void;
}

export default function FloatingMenu({
  anchorEl,
  open,
  setAnchorEl,
  component,
  handleClose,
}: Props) {
  const id = open ? "simple-popover" : undefined;

  return (
    <Popover
      id={id}
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top", // Adjust this to change how the Popover opens relative to the button
        horizontal: "left",
      }}
      PaperProps={{
        style: {
          width: "100vw", // Ensures the Paper component takes the full width
          maxWidth: "none", // Removes any max width constraints
          padding: "10px",
        },
      }}
      sx={{
        "& .MuiPopover-paper": {
          width: "100vw",
          height: "95vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "start",
          backgroundColor: "#111111",
          boxShadow: "none",
          zIndex: 0,
          overflow: "hidden",
          p: 0,
          left: "0px !important",
        },
        top: "20px",
      }}
    >
      {component}
    </Popover>
  );
}
