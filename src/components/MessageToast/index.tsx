import { closeToast, openToast, setHideToast } from "@/redux/slices/toastSlice";
import { RootState } from "@/redux/store";
import { useDispatch, useSelector } from "react-redux";
import "./index.css";
import { useEffect, useState } from "react";
import CloseIcon from "@mui/icons-material/Close";

const Toast = () => {
  const dispatch = useDispatch();
  const { isOpen, showToast } = useSelector((state: RootState) => state.toast);

  const msgs = useSelector((state: RootState) => state.appSlice.toastMsgs);

  const handleClose = () => {
    dispatch(closeToast());
    dispatch(setHideToast());
  };

  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (msgs.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % msgs.length);
        dispatch(closeToast());
        setTimeout(() => dispatch(openToast()), 200);
      }, 5000);

      return () => clearInterval(interval);
    }
    if (msgs.length === 1) {
      setCurrentIndex(0);
    }
  }, [msgs]);

  return (
    <>
      {isOpen && showToast && msgs?.[currentIndex] && (
        <div
          id="toast-undo"
          className="toast flex flex-col md:flex-row h-fit leading-loose items-start m-3 sm:w-11/12 bottom-0 left-0 md:left-auto md:top-20 md:right-5 md:max-w-xs p-1.5 md:p-5
           text-tx-primary rounded-2xl shadow-black shadow-2xl md:shadow bg-[#101010] border border-solid border-[#171717] gap-1 md:gap-5 font-manrope"
          role="alert"
          style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.5)" }}
        >
          <p className="text-sm tracking-wide leading-loose md:leading-relaxed font-normal p-3 md:p-0">
            {msgs?.[currentIndex]}
          </p>
          <button
            type="button"
            className="items-center hidden md:block h-min justify-center p-1.5 rounded-full bg-[#191919] hover:bg-[#161616] transition-all duration-150"
            onClick={handleClose}
            aria-label="Close"
          >
            <span className="sr-only">Close</span>
            <CloseIcon className="w-4 h-4 text-white" />
          </button>

          <div className="w-full flex text-tx-secondary flex-row gap-1 md:hidden">
            <button
              type="button"
              className="flex flex-1 items-center justify-center text-sm text-nowrap bg-[#161616] hover:bg-[#161616]  transition-all duration-150 p-2.5 rounded-xl"
              onClick={handleClose}
            >
              Close
            </button>
            <button
              type="button"
              className="flex flex-1 font-dmserif italic items-center justify-center text-sm text-nowrap bg-[#161616] hover:bg-[#161616] transition-all duration-150 p-2.5 rounded-xl"
              onClick={handleClose} //todo link to sign in
            >
              <p className="gradient_text w-full text-center">

              Save Money
              </p>
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default Toast;
