import { Separator } from "@/constants/icons";
import { EarnedBadges } from "@/constants/socialLinks";
import { Box, Typography } from "@mui/material";
import React from "react";
import Link from "next/link";

function EarnedBadgesComp() {
  return (
    <Box className="w-full flex flex-col items-center justify-center gap-5 py-2">
      <Box>
        <Typography className="text-tx-primary">Earned Badges</Typography>
      </Box>
      <Box className="flex justify-center w-[90%] md:w-full items-center gap-2">
        {EarnedBadges.map((item, index) => {
          if (!item?.isFooter) return null;

          const rel = item.doFollow ? "noopener follow" : undefined;

          return (
            <>
              <Link
                href={item.url}
                target="_blank"
                title={item.title}
                {...(rel ? { rel } : {})}
                className="w-fit"
              >
                {item.icon}
              </Link>
              {index !== EarnedBadges.length - 1 && <Separator />}
            </>
          );
        })}
      </Box>
    </Box>
  );
}

export default EarnedBadgesComp;
