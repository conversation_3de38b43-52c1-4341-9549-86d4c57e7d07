.box {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
  margin: 5px auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background: radial-gradient(47.57% 140.67% at 50% 50%, #0A0A0A 0%, #151515 57%, #0A0A0A 100%);
  border-radius: 25px;
  width: max-content;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
}

.nav {
  position: relative;
  display: flex;
}

.nav_gradient {
  background: radial-gradient(47.57% 140.67% at 50% 50%, #0A0A0A 0%, #151515 57%, #0A0A0A 100%);
}

@media (max-width: 767px) {
  .nav {
    margin: 3px;
    border-radius: 50%;
    border: 1px solid #181818;
  }
}

.nav_element {
  position: relative;
  display: inline-block;
  padding-bottom: 1px;
}

.nav_element::after {
  content: "";
  position: absolute;
  bottom: 0;
  /* Aligns just below the text */
  left: 50%;
  width: 0%;
  height: 1px;
  background: linear-gradient(to right, transparent, #b3b3b3, transparent);
  transition: width 0.5s ease, left 0.5s ease;
}

.nav_element:hover::after,
.nav_element_selected::after {
  width: 100%;
  left: 0;
}

.nav_element_selected {
  position: relative;
  display: inline-block;
  padding-bottom: 1.5px;
}

.nav_element_selected::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, #b3b3b3, transparent);
  border-radius: 1px;
}

.button {
  cursor: pointer;
  background-image: radial-gradient(ellipse at 50% 50%,
      #168ff9 0%,
      #001a59 100%);

  /* background-size: 200% 100%; */
  transition: all 1s;
  /* background-position: 0 0; */
  margin-right: -5px;
  z-index: 1000;
  border: 1px solid #0b57ad;
  font-family: Manrope;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.button:hover {
  background-position: 100% 250%;
}

@media (max-width: 767px) {
  .button {
    font-size: 13px;
  }
}