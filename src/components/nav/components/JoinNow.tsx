import { Button } from "@mui/material";
import Link from "next/link";
import styles from "../nav.module.css";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import { DASHBOARD_LINK } from "@/constants/vars";
import { HTMLAttributeAnchorTarget } from "react";

function JoinNow({
  size,
  text,
  className,
  link,
  target = "_blank",
}: {
  size: "small" | "medium";
  text?: string;
  className?: string;
  link?: string;
  target?: HTMLAttributeAnchorTarget;
}) {
  const isMobile = useMediaQuery("(max-width: 667px)");

  let style = size === "small" ? { padding: "5px 15px" } : {};

  return (
    <Link
      href={link || DASHBOARD_LINK}
      passHref={true}
      target={target}
      className={className || "h-full w-fit p-0"}
      title="Get Started"
    >
      <Button
        variant="contained"
        size={isMobile ? "small" : "medium"}
        className={`rounded-full w-full h-min text-nowrap text-white font-manrope font-normal text-sm md:text-base px-4 py-2 md:p-5 ${styles.button}`}
        color="primary"
        sx={style}
      >
        {text || "Deploy for Free"}
      </Button>
    </Link>
  );
}

export default JoinNow;
