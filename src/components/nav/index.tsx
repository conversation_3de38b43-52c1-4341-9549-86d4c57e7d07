"use client";

import { <PERSON><PERSON> } from "@/constants/icons";
import { LOGO } from "@/constants/vars";
import useMediaQuery from "@/hooks/useMediaQueryHook";

import { setIsHomePage } from "@/redux/slices/appSlice";
import { useAppDispatch, useAppSelector } from "@/redux/store";
import {
  Box,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography
} from "@mui/material";
import { motion } from "framer-motion";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { IoClose } from "react-icons/io5";
import JoinNow from "./components/JoinNow";
import styles from "./nav.module.css";

function Navbar() {
  const path = usePathname();
  const dispatch = useAppDispatch();

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const buttonRef: any = useRef(null); // Add this at the beginning of your component.

  const isMobile = useMediaQuery("(max-width: 1023px)");

  const [selected, setSelected] = useState("");

  const [isTop, setIsTop] = useState(true);

  const navigationRouter: any = useRouter();

  // let buttonPosition = 0; // Default position
  // if (buttonRef.current) {
  //   const rect = buttonRef.current.getBoundingClientRect();
  //   buttonPosition = rect.top + rect.height; // Calculate position based on the button's bottom
  // }

  const { isHomePage } = useAppSelector((state) => state.appSlice);
  const handleNavigation = (path: string, target: string) => {
    if (target === "_blank") {
      window.open(path, target);
    } else {
      navigationRouter.push(path);
      if (path === "/") {
        setSelected("home");
        dispatch(setIsHomePage(true));
      } else {
        setSelected(path.split("/")[1].toLowerCase());
        dispatch(setIsHomePage(false));
      }
    }
    handleClose();
  };

  useEffect(() => {
    if (path === "/") {
      setSelected("home");
      dispatch(setIsHomePage(true));
    } else {
      dispatch(setIsHomePage(false));
      setSelected(path.split("/")[1].toLowerCase());
    }
  }, [path]);

  useEffect(() => {
    if (!isMobile) setAnchorEl(null);
  }, [isMobile]);

  useEffect(() => {
    const handleScroll = () => setIsTop(!(window.scrollY > 45));

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    setIsTop(window.scrollY < 45);
  }, []);

  const navButtonVariants = {
    hidden: {
      x: "0%", // Start from the right side, off-screen
      opacity: 0,
    },
    visible: {
      x: 0, // Slide in to its original position
      opacity: 1,
      transition: {
        duration: 0.5, // Adjust the duration as needed
        ease: "easeIn", // Ease out for a smooth end of the entering animation
      },
    },
    exit: {
      x: "100%", // Slide out to the left side, off-screen
      opacity: 0,
      transition: {
        duration: 0.5,
        ease: "easeIn", // Ease in for a smooth start of the exiting animation
      },
    },
  };

  return (
    <Box
      className={`z-50 flex absolute justify-center items-center bg-none w-full p-0 md:px-5 md:py-${
        isHomePage ? "0" : "3"
      }`}
    >
      {/* web nav - fixed part */}

      <Box
        className={`hidden lg:flex w-full m-2 ${isHomePage && "mt-1"} px-3 py-${
          isHomePage ? "0" : "0"
        } md:px-0 justify-between items-center ${styles.nav}`}
      >
        <Link
          href="/"
          className="flex justify-center items-center gap-2 md:gap-2 no-underline text-inherit"
          title="Explore Kuberns for Free"
        >
          <img
            src={LOGO}
            alt="Logo Kuberns"
            className="w-auto h-8"
            title="Deploy your backend in one click with Kuberns"
          />
          <Typography
            className={`text-sm md:text-lg font-manrope gradient-text tracking-wide`}
          >
            Kuberns
          </Typography>
        </Link>

        <div className="hidden lg:flex gap-5 items-center justify-center">
          <JoinNow size="medium" />
        </div>
      </Box>

      {/* web nav - floating part */}
      <Box
        marginTop={`${isTop && isHomePage && "32px !important"}`}
        className={`hidden lg:flex justify-center items-center fixed z-[49] top-4 border border-[#272727]
           ${!isTop ? "py-1 pr-1 pl-8" : "py-2.5 px-8"} ${styles.box} ${
          !isTop ? "gap-5" : "gap-0"
        }`}
      >
        <Box className="gap-6 flex">
          {navLinks.map((item) => {
            return (
              <Box
                className={`no-underline cursor-pointer text-tx-tertiary
                  duration-600 ease-in-out text-base ${
                    selected === item.name.toLowerCase()
                      ? styles.nav_element_selected +
                        " font-medium gradient_text "
                      : styles.nav_element
                  }`}
                onClick={() => handleNavigation(item.url, item.target)}
              >
                {item.name}
              </Box>
            );
          })}
        </Box>

        <motion.div
          variants={navButtonVariants} // Variants for animation states
          initial="hidden" // Initial animation state
          animate={!isTop ? "visible" : "exit"} // Control the animation based on the open variable
          className="hidden md:flex justify-center items-center w-full"
        >
          {!isTop && (
            <div className="flex justify-center items-center w-full">
              <JoinNow size="small" />
            </div>
          )}
        </motion.div>
      </Box>

      {/* mobile nav */}
      <Box
        sx={{
          marginTop:
            isHomePage && isTop ? "42px !important" : "10px !important",
          top: isHomePage && isTop ? 5 : 4,
        }}
        className={`fixed left-0 right-0 mx-auto w-[calc(100%-10px)] z-100 flex flex-col lg:hidden ${
          open ? "rounded-3xl" : "rounded-full"
        } border border-[#181818]  p-1 justify-between items-center ${
          styles.nav_gradient
        }`}
      >
        <Box className="flex pl-3 justify-between items-center w-full">
          <Link
            href="/"
            className="flex justify-center items-center gap-2 md:gap-2 no-underline text-inherit"
            title="Explore Kuberns for Free"
          >
            <img
              src={LOGO}
              alt="Logo Kuberns"
              className="w-auto h-8"
              title="Deploy your backend in one click with Kuberns"
            />
            <Typography
              className={`text-sm md:text-lg font-manrope gradient-text tracking-wide`}
            >
              Kuberns
            </Typography>
          </Link>

          <Box className="flex items-center justify-center w-fit gap-1">
            <JoinNow size="small" />
            <Button
              onClick={!open ? handleClick : handleClose}
              className=" bg-stone-900 border border-stone-800 rounded-full p-2 flex items-center justify-center"
            >
              {!open ? (
                <Hamburger
                  height={isMobile ? "22" : "28"}
                  width={isMobile ? "22" : "28"}
                />
              ) : (
                <IoClose size={isMobile ? 25 : 28} color="#c7c8c9" />
              )}
            </Button>
          </Box>
        </Box>

        {open && (
          <List
            className={`w-full h-fit flex flex-col gap-1 p-0 mt-1 pt-1 border-t border-stone-900 border-dashed`}
          >
            {navLinks?.map((link, index) => (
              <ListItem
                key={link.id}
                className="px-5 py-2 bg-stone-800/25 rounded-full"
              >
                <ListItemButton
                  sx={{
                    padding: 0,
                  }}
                  onClick={() => handleNavigation(link.url, link.target)}
                >
                  <ListItemText
                    primary={link.name}
                    className="font-manrope font-medium text-tx-primary"
                  />
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        )}
      </Box>

      {/* 
      {!isTop && (
        <div className="hidden lg:flex">
          <ButtonWithIcon
            url="https://calendly.com/kuberns"
            text={"Schedule a"}
            italicText="Demo"
            icon={
              <Image src={CALENDLY} width={20} height={20} alt="calendly" />
            }
            prefix={true}
          />
        </div>
      )} */}
    </Box>
  );
}

export default Navbar;

export const navLinks = [
  {
    id: 1,
    name: "Home",
    url: "/",
    target: "_self",
  },
  {
    id: 2,
    name: "About",
    url: "/about",
    target: "_self",
  },
  {
    id: 3,
    name: "Pricing",
    url: "/pricing",
    target: "_self",
  },
  {
    id: 4,
    name: "Contact",
    url: "/contact",
    target: "_self",
  },
  {
    id: 5,
    name: "Templates",
    url: "/templates",
    target: "_self",
  },
  {
    id: 6,
    name: "Docs",
    url: "https://docs.kuberns.com/",
    target: "_blank",
  },
  {
    id: 7,
    name: "Blogs",
    url: "/blogs",
    target: "_self",
  },
];
