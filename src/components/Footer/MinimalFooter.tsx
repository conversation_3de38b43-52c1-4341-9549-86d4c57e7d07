"use client";

import { ArrowUpRight, Separator } from "@/constants/icons";
import { SocialLinks } from "@/constants/socialLinks";
import { Mail } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/navigation";
import EarnedBadgesComp from "../EarnedBadges";
import PageLayout from "../Layout";

const mailto = (email: string) => {
  // Copy the email to the clipboard
  navigator.clipboard
    .writeText(email)
    .then(() => {
      console.log("Email address copied to clipboard");
    })
    .catch((err) => {
      console.error("Failed to copy email address to clipboard", err);
    });

  // Open the default email client
  window.location.href = `mailto:${email}`;
};

function MinimalFooter() {
  const router = useRouter();

  const handleNavigation = (path: string, target: string) =>
    (path !== "#" && target !== "_blank" && router.push(path)) ||
    window.open(path, target);

  const footerLinks = [
    {
      id: 1,
      title: "About Us",
      url: "/about",
      target: "_self",
    },
    {
      id: 2,
      title: "Documentation",
      url: "https://docs.kuberns.com/",
      target: "_blank",
    },
  ];

  return (
    <div className="fixed bottom-0 w-full">
      <Box className="w-full pt-5 md:pt-0">
        <EarnedBadgesComp />
      </Box>
      <Box
        className={`flex flex-col p-4 py-6 md:px-16 md:py-6 items-center justify-center font-manrope bg-bg-secondary mt-8 rounded-t-3xl z-[9999px]`}
        style={{
          borderTop: "2px solid #212121",
        }}
      >
        <PageLayout className="flex flex-col gap-4 justify-between items-center w-full">
          <Box className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
            {/* Logo & Description */}
            <Box className="flex flex-col w-full py-2 md:py-5 gap-2.5">
              <Typography
                className="text-br-secondary font-manrope font-semibold text-lg md:text-xl cursor-pointer"
                onClick={() => handleNavigation("/", "_self")}
              >
                Kuberns
              </Typography>
              <Typography className="text-tx-primary font-manrope text-sm md:text-base md:w-1/2">
                AI-Powered Cloud Autopilot for One Click Deployments
              </Typography>
            </Box>

            {/* Links & Mail */}
            <Box className="flex flex-col w-full md:justify-center md:items-center">
              <Box className="flex flex-col justify-center items-start gap-1">
                {/* Links */}
                {footerLinks.map((link) => (
                  <Box
                    className="flex flex-col justify-center items-center md:px-3 py-1 cursor-pointer rounded hover:bg-stone-800"
                    onClick={() => handleNavigation(link.url, link.target)}
                    key={link.id}
                  >
                    <Box className="flex items-center w-full gap-2">
                      <ArrowUpRight />
                      <Typography className="text-tx-primary font-manrope font-medium md:font-semibold text-sm">
                        {link.title}
                      </Typography>
                    </Box>
                  </Box>
                ))}

                {/* Mail button */}
                <button
                  className="flex flex-col justify-center items-center md:px-3 py-1 cursor-pointer rounded hover:bg-stone-800"
                  onClick={() => mailto("<EMAIL>")}
                >
                  <Box className="flex items-center w-full gap-2">
                    <Mail className="text-tx-secondary text-base" />
                    <Typography className="text-tx-primary font-manrope font-medium md:font-semibold text-sm">
                      <EMAIL>
                    </Typography>
                  </Box>
                </button>
              </Box>
            </Box>

            {/* Socials & Copyright */}
            <Box className="flex md:flex-col items-center justify-between flex-wrap pt-5 md:justify-center md:items-end py-2 gap-5 md:gap-4">
              <Box className="grid w-fit text-tx-primary font-manrope font-medium text-sm justify-center md:justify-start">
                © Kuberns {new Date().getFullYear()}
              </Box>
              <Box className="flex justify-center w-full md:w-fit items-center gap-1 md:gap-3">
                <Link
                  href={"/privacy-policy"}
                  className="text-tx-secondary font-manrope font-medium text-xs sm:text-sm"
                  title="Privacy Policy"
                >
                  Privacy Policy
                </Link>
                <Separator />
                <Link
                  href={"/terms-conditions"}
                  className="text-tx-secondary font-manrope font-medium text-xs sm:text-sm"
                  title="Terms of Service"
                >
                  Terms of Service
                </Link>
                <Separator />
                <Link
                  href={"/refunds"}
                  className="text-tx-secondary font-manrope font-medium text-xs sm:text-sm"
                  title="Cancellations and Refunds"
                >
                  Cancellations & Refunds
                </Link>
              </Box>
              <Box className="flex justify-center w-full md:w-fit items-center gap-2 ">
                {SocialLinks.map((item, index) => {
                  if (!item?.isFooter) return null;
                  const rel = item.doFollow ? "noopener follow" : undefined;
                  return (
                    <>
                      <Link
                        href={item.url}
                        target="_blank"
                        title={item.title}
                        {...(rel ? { rel } : {})}
                      >
                        {item.icon}
                      </Link>
                      {SocialLinks?.[index + 1]?.isFooter && <Separator />}
                    </>
                  );
                })}
              </Box>
            </Box>
          </Box>
        </PageLayout>
      </Box>
    </div>
  );
}

export default MinimalFooter;
