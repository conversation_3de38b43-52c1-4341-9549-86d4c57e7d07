"use client";

import React from "react";
import PageLayout from "../Layout";
import { Box, Typography } from "@mui/material";
import { Manrope } from "next/font/google";
import { Gith<PERSON>, LinkedIn, Separator, Twitter } from "@/constants/icons";
import useMediaQuery from "@/hooks/useMediaQueryHook";
import { useRouter } from "next/navigation";

const manrope = Manrope({ subsets: ["latin"] });

function Footer() {
  const isMobile = useMediaQuery("(max-width: 767px)");

  const router = useRouter();

  const handleNavigation = (path: string) => path !== "#" && router.push(path);

  return (
    <Box
      className={`flex flex-col p-8 pb-10 items-center ${manrope.className} bg-bg-secondary mt-24 rounded-t-3xl z-[9999px]`}
      style={{
        borderTop: "2px solid #212121",
      }}
    >
      <PageLayout className="flex flex-col gap-4 justify-between items-center">

        <Box className="flex flex-col md:flex-row w-full gap-4 md:gap-0 justify-between items-start">
          <Box
            className="flex flex-col w-full p-2 md:p-5 gap-2.5"
            sx={{
              flex: 1,
            }}
          >
            <Typography
              className="text-br-primary font-manrope font-semibold text-lg md:text-xl cursor-pointer"
              onClick={() => handleNavigation("/")}
            >
              Kuberns
            </Typography>
            <Typography className="text-tx-primary font-manrope font-medium text-sm md:text-base">
              AI-Powered Cloud Autopilot for One Click Deployments.
            </Typography>
          </Box>

          <Box
            className="flex justify-between md:justify-evenly items-center p-2 md:p-5 w-full"
            sx={{
              flex: isMobile ? 1 : 3,
            }}
          >
            {footerLinks.map((link) => (
              <Box
                className="flex flex-col justify-start items-start"
                key={link.id}
              >
                <Typography className="text-tx-primary font-manrope font-semibold upper text-sm md:text-base ">
                  {link.title}
                </Typography>
                <Box className="h-1" />
                {link.links.map((item) => (
                  <Typography
                    className="text-tx-secondary font-manrope font-semibold text-xs md:text-sm pt-1.5 flex justify-center items-center gap-2 hover:text-[#fff] duration-300 ease-in-out cursor-pointer"
                    key={item.id}
                    onClick={() => handleNavigation(item.url)}
                  >
                    {item.title} {item?.chip}
                  </Typography>
                ))}
              </Box>
            ))}
          </Box>
        </Box>

        <span
          style={{
            border: "1px solid #272727",
            width: "100%",
          }}
        />

        <Box className="grid grid-cols-1 md:grid-cols-4 w-full justify-center items-center gap-3 md:gap-0 md:justify-between px-5 pt-2 md:pt-5">
          <Box className="grid text-tx-primary font-manrope font-medium text-sm justify-center md:justify-start">
            © Kuberns {new Date().getFullYear()}
          </Box>
          <Box className="grid gap-2 col-span-2">
            <Box className="flex justify-center items-center gap-2 md:gap-3">
              <Typography className="text-tx-secondary font-manrope font-medium text-sm">
                Privacy Policy
              </Typography>
              <Separator />
              <Typography className="text-tx-secondary font-manrope font-medium text-sm">
                Terms of Service
              </Typography>
              <Separator />
              <Typography className="text-tx-secondary font-manrope font-medium text-sm">
                Security
              </Typography>
            </Box>
          </Box>
          <Box className="grid text-tx-primary text-sm justify-center md:justify-end md:pl-16">
            <Box className="flex justify-center items-center gap-5">
              <Twitter />
              <LinkedIn />
              <Github />
            </Box>
          </Box>
        </Box>
      </PageLayout>
    </Box>
  );
}

export default Footer;

const footerLinks = [
  {
    id: "1",
    title: "Main",
    links: [
      {
        id: "1-1",
        title: "Blog",
        url: "#",
      },
      {
        id: "1-2",
        title: "Career",
        url: "#",
      },
      {
        id: "1-3",
        title: "FAQs",
        url: "#",
        chip: (
          <Box
            className="bg-[#004259] text-[#00AFED] rounded-md px-1 text-xs"
            sx={{
              border: "1px solid #00AFED",
            }}
          >
            New
          </Box>
        ),
      },
      {
        id: "1-4",
        title: "About Us",
        url: "/about",
      },
    ],
  },
  {
    id: "2",
    title: "Developer Docs",
    links: [
      {
        id: "2-1",
        title: "Developer API",
        url: "#",
      },
      {
        id: "2-2",
        title: "Documentation",
        url: "#",
      },
      {
        id: "2-3",
        title: "Demo Apps",
        url: "#",
      },
      {
        id: "2-4",
        title: "Guides",
        url: "#",
      },
    ],
  },
  {
    id: "3",
    title: "Contact us",
    links: [
      {
        id: "3-1",
        title: "Email",
        url: "#",
      },
      {
        id: "3-2",
        title: "Support",
        url: "#",
      },
      {
        id: "3-3",
        title: "Community",
        url: "#",
      },
      {
        id: "3-4",
        title: "Blog",
        url: "#",
        chip: (
          <Box
            className="bg-[#433400] text-[#AF8800] rounded-md px-1 text-xs"
            sx={{
              border: "1px solid #AF8800",
            }}
          >
            New
          </Box>
        ),
      },
    ],
  },
];
