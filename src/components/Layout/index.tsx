import cn from "@/helpers/cn";
import React from "react";

function PageLayout(props: any) {
  return (
    <div className="w-full flex justify-center items-center">
      <div
        className={cn(
          "w-full flex justify-center items-center px-5 xl:px-0 max-w-none xl:max-w-[1400px]",
          props.className
        )}
        style={{ ...props?.style }}
        id={props?.id || ""}
      >
        {props.children}
      </div>
    </div>
  );
}

export default PageLayout;
