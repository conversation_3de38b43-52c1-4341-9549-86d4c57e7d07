'use client'

import { OnGoingConstruction } from "@/constants/icons";
import { Box, Typography } from "@mui/material";
import { Manrope } from "next/font/google";
import React from "react";
import styles from "./UD.module.css";
import useMediaQuery from "@/hooks/useMediaQueryHook";
const manrope = Manrope({ subsets: ["latin"] });

function UnderConstruction() {
  const isMobile = useMediaQuery("(max-width: 767px)");

  return (
    <Box
      className={`flex flex-col w-full justify-center items-center pt-5 pb-10 gap-8 font-manrope`}
    >
      <OnGoingConstruction width={isMobile ? "400" : "450"} height={isMobile ? "400" : "450"} />
      <div className="flex flex-col gap-3 w-full items-center justify-center">
        <Typography className={"font-manrope " + styles.header_text}>
          Hold onto your hats, it's almost here!
        </Typography>
        <Typography className={"font-manrope w-1/2 leading-relaxed font-medium " + styles.desc_text}>
          We're setting up a dedicated support system to provide you with prompt assistance 
          and expert guidance. We'll be ready to help you soon!
        </Typography>
      </div>
      <Box className={"font-manrope " + styles.stay_tuned}>Stay Tuned</Box>
    </Box>
  );
}

export default UnderConstruction;
