.header_text {
  font-size: 26px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  background: var(--Text-Gradient,
      radial-gradient(115.98% 49.33% at 48.54% 50%,
        #fcfcfc 0%,
        rgba(252, 252, 252, 0.45) 100%));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.desc_text {
  color: #9c9c9c;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
  /* 25.6px */
}

.stay_tuned {
  display: flex;
  padding: var(--Radius-md, 10px) 20px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-weight: 500;
  gap: 25px;
  border-radius: 37px;
  border: 1px solid #2a2a2a;
  background: #141414;
  cursor: pointer;
  color: #cacaca;
}

@media screen and (max-width: 767px) {
  .header_text {
    font-size: 20px;
  }

  .desc_text {
    width: 90%;
    font-size: 14px;
  }

  .stay_tuned {
    gap: 15px;
    font-size: 14px;
    padding: var(--Radius-md, 8px) 15px;
  }

  .stay_tuned:hover {
    background: #2a2a2a;
  }
}