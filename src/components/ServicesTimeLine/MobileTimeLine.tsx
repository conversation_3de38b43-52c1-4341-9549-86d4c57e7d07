import {
  ANGULAR,
  DJ<PERSON>GO,
  MYSQL,
  NODEJS,
  POSTGRESQL,
  REACT,
  REDIS,
  VUE
} from "@/constants/vars";
import { Box, Typography } from "@mui/material";
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";
import { Manrope } from "next/font/google";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { DiRedis } from "react-icons/di";
import { FaDatabase } from "react-icons/fa";
import { FiServer } from "react-icons/fi";
import { TiDeviceLaptop } from "react-icons/ti";

const manrope = Manrope({ subsets: ["latin"] });

function MobileTimeLine() {
  const pathRef: any = useRef(null);
  const pinDiv: any = useRef(null);

  const [progress, setProgress] = useState(0);

  const [activeBox, setActiveBox] = useState(0);

  useEffect(() => {
    if (progress > 2 && progress < 15) {
      setActiveBox(1);
    } else if (progress > 15 && progress < 25) {
      setActiveBox(2);
    } else if (progress > 28 && progress < 55) {
      setActiveBox(3);
    } else if (progress > 55) {
      setActiveBox(4);
    } else {
      setActiveBox(0);
    }
  }, [progress]);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    const path = pathRef.current;
    const pin = pinDiv.current;

    if (path && pin) {
      const pathLength = path.getTotalLength();
      path.style.strokeDasharray = `${pathLength}`;
      path.style.strokeDashoffset = `${pathLength}`;
      pin.style.transition = "top 0.5s, left 0.5s, width 0.5s";

      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: path,
          start: () => `top+=100 center`,
          end: () => `bottom center`,
          scrub: true,
          onUpdate: (self) => {
            setProgress(Math.round(self.progress * 100));
          },
          onToggle: ({ isActive }) => {
            gsap.to(pin, {
              // position: isActive ? "fixed" : "relative",
              top: isActive ? 0 : "auto",
              left: isActive ? 0 : "auto",
              duration: 0.5,
              width: isActive ? "100%" : "auto",
            });
          },
        },
      });

      tl.to(path, {
        strokeDashoffset: 0,
        stroke: "#1054ff",
        scrollBehavior: "smooth",
      });
    }
  }, []);

  return (
    <Box
      className={`w-full flex justify-center items-center relative ${manrope.className}`}
    >
      <div
        ref={pinDiv}
        className="flex justify-start items-start md:justify-center md:items-center w-full min-h-[85vh]"
      >
        <Box className="flex justify-center items-center h-full w-full relative">
          <svg
            width="8"
            height="600"
            viewBox="0 0 8 600"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="absolute z-[1]"
          >
            <path
              ref={pathRef}
              id="myPath"
              d="M4 0.232422V599.716"
              stroke="#202535"
              stroke-width="8"
              className="absolute"
            />
          </svg>

          <svg
            width="8"
            height="600"
            viewBox="0 0 8 600"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              className="absolute z-[1]"
              d="M4 0.232422V599.716"
              stroke="#202535"
              stroke-width="8"
            />
          </svg>

          <Box
            className={`w-[300px] bg-bg-secondary rounded-xl border border-st-light   shadow-xl z-10 absolute top-[-10%] text-tx-primary font-manrope font-medium leading-relaxed 
              flex justify-start items-start p-4 ${activeBox === 1 ? "h-fit scale-[120%]" : "h-[100px] scale-100"
              } duration-300 ease-in-out`}
          >
            <Box className="flex flex-col w-full h-full justify-center items-center relative gap-1">
              <Box
                className={`flex w-full items-center ${activeBox === 1
                    ? "justify-start text-base text-br-secondary "
                    : "justify-center text-2xl"
                  } gap-2 duration-300 ease-in-out`}
              >
                <FiServer />
                <Typography
                  className={`${activeBox === 1 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out`}
                >
                  Back-end
                </Typography>
              </Box>
              {activeBox === 1 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  We offer backend service with background workers like celery,
                </Typography>
              )}
              <Box className="fixed top-[-28px] left-5 w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={DJANGO} alt="django" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
              <Box className="fixed bottom-[-15px] right-2 w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={NODEJS} alt="django" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>

          <Box
            className={`w-[300px] bg-bg-secondary rounded-xl border border-st-light   shadow-xl z-10 absolute top-[20%] text-tx-primary font-manrope font-medium leading-relaxed  
              flex justify-start items-start p-4  ${activeBox === 2 ? "h-fit scale-[120%]" : "h-[100px] scale-100"
              } duration-300 ease-in-out`}
          >
            <Box className="flex flex-col w-full h-full items-center justify-center gap-1 relative">
              <Box
                className={`flex w-full h-full items-center ${activeBox === 2
                    ? "justify-start text-base text-br-secondary "
                    : "justify-center text-2xl"
                  }  duration-300 ease-in-out gap-2`}
              >
                <TiDeviceLaptop />
                <Typography
                  className={`${activeBox === 2 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out flex ${activeBox === 2 ? "flex-row" : "flex-col"
                    } justify-center items-center gap-2`}
                >
                  Front-end{" "}
                  <span className="text-sm text-orange-500">(coming soon)</span>
                </Typography>
              </Box>
              {activeBox === 2 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  We provide a sleek, responsive front-end architecture
                  frameworks like React or Vue.js, for a seamless user
                  experience.
                </Typography>
              )}
              <Box className="fixed top-[-28px] left-5 w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={REACT} alt="django" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
              <Box className="fixed top-[-28px] right-5 w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={ANGULAR} alt="django" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
              <Box className="fixed bottom-[-28px] right-[126px] w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={VUE} alt="django" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>

          <Box
            className={`w-[300px] bg-bg-secondary rounded-xl border border-st-light   shadow-xl z-10 absolute top-[53%] text-tx-primary font-manrope font-medium leading-relaxed  
              flex justify-start items-start p-4 ${activeBox === 3 ? "h-fit scale-[120%]" : "h-[100px] scale-100"
              } duration-300 ease-in-out`}
          >
            <Box className="flex flex-col w-full h-full items-center justify-center gap-1 relative">
              <Box
                className={`flex w-full h-full items-center ${activeBox === 3
                    ? "justify-start text-base text-br-secondary "
                    : "justify-center text-2xl"
                  } gap-2 duration-300 ease-in-out`}
              >
                <FaDatabase />
                <Typography
                  className={`${activeBox === 3 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out`}
                >
                  Database
                </Typography>
              </Box>
              {activeBox === 3 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  Our database service utilizes advanced SQL and NoSQL solutions
                  for secure, scalable data storage and retrieval.
                </Typography>
              )}
              <Box className="fixed top-[-25px] right-[-18px] w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={POSTGRESQL} alt="django" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
              <Box className="fixed bottom-[-25px] left-[-18px] w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={MYSQL} alt="django" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>

          <Box
            className={`w-[300px] bg-bg-secondary rounded-xl border border-st-light   shadow-xl z-10 absolute bottom-[0%] text-tx-primary font-manrope font-medium leading-relaxed  
              flex justify-start items-start p-4 ${activeBox === 4 ? "h-fit scale-[120%]" : "h-[100px] scale-100"
              } duration-300 ease-in-out`}
          >
            <Box className="flex flex-col w-full h-full items-center justify-center gap-1 relative">
              <Box
                className={`flex w-full h-full items-center ${activeBox === 4
                    ? "justify-start text-base text-br-secondary "
                    : "justify-center text-2xl"
                  } gap-2 duration-300 ease-in-out`}
              >
                <DiRedis size={activeBox === 4 ? 25 : 35} />
                <Typography
                  className={`${activeBox === 4 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out`}
                >
                  Redis
                </Typography>
              </Box>
              {activeBox === 4 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  We offer Redis as a high-performance data store, ideal for
                  caching and real-time application requirements.
                </Typography>
              )}
              <Box className="fixed top-[-25px] right-[30px] w-10 h-10 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={REDIS} alt="redis" width={25} height={25} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>
        </Box>
      </div>
    </Box>
  );
}

export default MobileTimeLine;
