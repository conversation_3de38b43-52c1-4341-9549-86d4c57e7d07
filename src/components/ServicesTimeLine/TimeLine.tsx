import {
  ANGULAR,
  BG_NOISE_IMAGE_URL,
  DJANGO,
  MYSQL,
  NODEJS,
  POSTGRESQL,
  REACT,
  REDIS,
  VUE,
} from "@/constants/vars";
import { setIsAnimationActive } from "@/redux/slices/appSlice";
import { useAppDispatch } from "@/redux/store";
import { Box, Typography } from "@mui/material";
import gsap from "gsap";
import ScrollTrigger from "gsap/ScrollTrigger";
import { Manrope } from "next/font/google";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { DiRedis } from "react-icons/di";
import { FaDatabase } from "react-icons/fa";
import { FiServer } from "react-icons/fi";
import { TiDeviceLaptop } from "react-icons/ti";

const manrope = Manrope({ subsets: ["latin"] });

const ServiceTimeLine = () => {
  const dispatch = useAppDispatch();

  const pathRef: any = useRef(null);
  const pinDiv: any = useRef(null);

  const [progress, setProgress] = useState(0);

  const [activeBox, setActiveBox] = useState(1);

  useEffect(() => {
    if (progress < 5) {
      setActiveBox(1);
    } else if (progress > 15 && progress < 25) {
      setActiveBox(2);
    } else if (progress > 28 && progress < 40) {
      setActiveBox(3);
    } else if (progress > 48 && progress < 80) {
      setActiveBox(4);
    } else if (progress > 80) {
      setActiveBox(1);
    }
  }, [progress]);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    const path = pathRef.current;
    const pin = pinDiv.current;

    if (path && pin) {
      const pathLength = path.getTotalLength();
      path.style.strokeDasharray = `${pathLength}`;
      path.style.strokeDashoffset = `-${pathLength}`;
      pin.style.transition = "top 0.5s, left 0.5s, width 0.5s";

      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: path,
          start: () => `center center`,
          end: () => `bottom+=2000 center`,
          scrub: true,
          onUpdate: (self) => {
            setProgress(Math.round(self.progress * 100));
          },
          onToggle: ({ isActive }) => {
            dispatch(setIsAnimationActive(isActive));
            gsap.to(pin, {
              position: isActive ? "fixed" : "absolute",
              top: 0,
              left: 0,
              width: isActive ? "100%" : "auto",
              opacity: isActive ? 1 : 0,
              display: isActive ? "flex" : "none",
            });
          },
        },
      });

      tl.to(path, {
        strokeDashoffset: 0,
        stroke: "#1054ff",
        scrollBehavior: "smooth",
      });
    }
  }, []);

  return (
    <Box
      className={`w-full flex justify-center items-center relative ${manrope.className} polka`}
    >
      <div
        ref={pinDiv}
        className="flex justify-start items-start md:justify-center md:items-center w-full min-h-screen "
      >
        <Box className="flex justify-center items-center h-[505px] w-[937px] relative">
          <svg
            width="937"
            height="505"
            viewBox="0 0 937 505"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              ref={pathRef}
              d="M215.5 116.5H40C21.2223 116.5 6 131.722 6 150.5V456C6 474.778 21.2223 490 40 490H375.896C386.374 490 396.267 485.169 402.709 476.906L517.05 330.261C523.345 322.187 532.943 317.38 543.178 317.174L890.685 310.171C909.192 309.798 924 294.689 924 276.178V40C924 21.2223 908.778 6 890 6H734.727C721.738 6 709.883 13.4012 704.18 25.0718L668.82 97.4282C663.117 109.099 651.262 116.5 638.273 116.5H219.5"
              id="myPath"
              stroke="#202535"
              strokeWidth="11.3579"
              fill="none"
            />
          </svg>
          <svg
            width="937"
            height="505"
            viewBox="0 0 937 505"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="absolute z-[-1]"
          >
            <path
              d="M215.5 116.5H40C21.2223 116.5 6 131.722 6 150.5V456C6 474.778 21.2223 490 40 490H375.896C386.374 490 396.267 485.169 402.709 476.906L517.05 330.261C523.345 322.187 532.943 317.38 543.178 317.174L890.685 310.171C909.192 309.798 924 294.689 924 276.178V40C924 21.2223 908.778 6 890 6H734.727C721.738 6 709.883 13.4012 704.18 25.0718L668.82 97.4282C663.117 109.099 651.262 116.5 638.273 116.5H219.5"
              stroke="#202535"
              strokeWidth="11.3579"
              fill="none"
            />
          </svg>
          <Box
            className={`h-[140px] w-[280px] bg-bg-secondary rounded-xl border border-dashed border-st-light shadow-xl z-[2px] absolute top-[10%] left-[10%] text-tx-primary font-manrope font-medium leading-relaxed flex justify-start items-start  ${activeBox === 1 ? "scale-125" : "scale-100"
              } duration-300 ease-in-out`}
          >
            {/* Background Image Box */}
            <Box
              className="absolute w-full h-full"
              sx={{
                background: BG_NOISE_IMAGE_URL,
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover", // Ensures the image covers the entire box without distortion
                backgroundPosition: "center", // Centers the image both horizontally and vertically
                zIndex: -1, // Ensures the background is behind all content
              }}
            />

            <Box className="flex flex-col w-full h-full items-center justify-center gap-2 relative p-5">
              <Box
                className={`flex w-full h-fit items-center  ${activeBox === 1
                  ? "justify-start text-base text-br-secondary"
                  : "justify-center text-2xl"
                  } gap-2 duration-300 ease-in-out`}
              >
                <FiServer />
                <Typography
                  className={`${activeBox === 1 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out font-manrope font-medium `}
                >
                  Back-end
                </Typography>
              </Box>
              {activeBox === 1 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  We offer backend service with background workers like celery,
                  celerybeat etc and custom runtime for application.
                </Typography>
              )}
              <Box className="fixed top-[-28px] left-5 w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={DJANGO} alt="django" width={35} height={35} title="Deploy your backend in one click with Kuberns" />
              </Box>
              <Box className="fixed bottom-[-28px] right-5 w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={NODEJS} alt="nodejs" width={35} height={35} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>

          <Box
            className={`h-[140px] w-[280px] bg-bg-secondary rounded-xl border border-dashed border-st-light shadow-xl z-10 absolute top-[20%] left-[85%] text-tx-primary font-manrope font-medium leading-relaxed  flex justify-start items-start   ${activeBox === 2 ? "scale-125" : "scale-100"
              } duration-300 ease-in-out`}
          >
            <Box
              className="absolute w-full h-full"
              sx={{
                background: BG_NOISE_IMAGE_URL,
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover", // Ensures the image covers the entire box without distortion
                backgroundPosition: "center", // Centers the image both horizontally and vertically
                zIndex: -1, // Ensures the background is behind all content
              }}
            />
            <Box className="flex flex-col w-full h-full items-center justify-center gap-2 relative  p-5">
              <Box
                className={`flex w-full h-fit items-center  ${activeBox === 2
                  ? "justify-start text-base text-br-secondary"
                  : "justify-center text-2xl"
                  } gap-2 duration-300 ease-in-out`}
              >
                <TiDeviceLaptop />
                <Typography
                  className={`${activeBox === 2 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out flex justify-center items-center gap-2 font-manrope font-medium `}
                >
                  Front-end{" "}
                  <span className="text-sm text-orange-500 font-manrope font-medium">
                    (coming soon)
                  </span>
                </Typography>
              </Box>
              {activeBox === 2 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  We provide a sleek, responsive front-end architecture
                  frameworks like React or Vue.js, for a seamless user
                  experience.
                </Typography>
              )}
              <Box className="fixed top-[-28px] left-5 w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={REACT} alt="react" width={35} height={35} title="Deploy your backend in one click with Kuberns" />
              </Box>
              <Box className="fixed top-[-28px] right-5 w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={ANGULAR} alt="angular" width={35} height={35} title="Deploy your backend in one click with Kuberns" />
              </Box>
              <Box className="fixed bottom-[-28px] right-[126px] w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={VUE} alt="vue" width={35} height={35} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>
          <Box
            className={`h-[140px] w-[280px] bg-bg-secondary rounded-xl border border-dashed border-st-light shadow-xl z-10 absolute bottom-[20%] left-[45%] text-tx-primary font-manrope font-medium leading-relaxed  flex justify-start items-start ${activeBox === 3 ? "scale-125" : "scale-100"
              } duration-300 ease-in-out`}
          >
            <Box
              className="absolute w-full h-full"
              sx={{
                background: BG_NOISE_IMAGE_URL,
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover", // Ensures the image covers the entire box without distortion
                backgroundPosition: "center", // Centers the image both horizontally and vertically
                zIndex: -1, // Ensures the background is behind all content
              }}
            />
            <Box className="flex flex-col w-full h-full items-center justify-center gap-2 relative p-5 ">
              <Box
                className={`flex w-full h-fit items-center  ${activeBox === 3
                  ? "justify-start text-base text-br-secondary"
                  : "justify-center text-2xl"
                  } gap-2 duration-300 ease-in-out`}
              >
                <FaDatabase />
                <Typography
                  className={`${activeBox === 3 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out font-manrope font-medium `}
                >
                  Database
                </Typography>
              </Box>
              {activeBox === 3 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  Our database service utilizes advanced SQL and NoSQL solutions
                  for secure, scalable data storage and retrieval.
                </Typography>
              )}
              <Box className="fixed top-[-25px] right-[-18px] w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image
                  src={POSTGRESQL}
                  alt="postgresql"
                  width={35}
                  height={35}
                  title="Deploy your backend in one click with Kuberns"
                />
              </Box>
              <Box className="fixed bottom-[-25px] left-[-18px] w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={MYSQL} alt="mysql" width={35} height={35} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>
          <Box
            className={`h-[140px] w-[280px] bg-bg-secondary rounded-xl border border-dashed border-st-light shadow-xl z-10 absolute top-[80%] right-[75%] text-tx-primary font-manrope font-medium leading-relaxed  flex justify-start items-start ${activeBox === 4 ? "scale-125" : "scale-100"
              } duration-300 ease-in-out`}
          >
            <Box
              className="absolute w-full h-full"
              sx={{
                background: BG_NOISE_IMAGE_URL,
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover", // Ensures the image covers the entire box without distortion
                backgroundPosition: "center", // Centers the image both horizontally and vertically
                zIndex: -1, // Ensures the background is behind all content
              }}
            />
            <Box className="flex flex-col w-full h-full items-center justify-center gap-2 relative  p-5">
              <Box
                className={`flex w-full h-fit items-center  ${activeBox === 4
                  ? "justify-start text-base text-br-secondary"
                  : "justify-center text-2xl"
                  } gap-2 duration-300 ease-in-out`}
              >
                <DiRedis size={activeBox === 4 ? 25 : 35} />
                <Typography
                  className={`${activeBox === 4 ? "text-base" : "text-xl"
                    } duration-300 ease-in-out font-manrope font-medium `}
                >
                  Redis
                </Typography>
              </Box>
              {activeBox === 4 && (
                <Typography
                  className={`text-xs duration-300 ease-in-out text-tx-primary font-manrope font-medium leading-relaxed `}
                >
                  We offer Redis as a high-performance data store, ideal for
                  caching and real-time application requirements.
                </Typography>
              )}
              <Box className="fixed top-[-25px] left-[30px] w-12 h-12 bg-[#202023] shadow-sm shadow-[#0D0D0D] flex justify-center items-center rounded-lg">
                <Image src={REDIS} alt="redis" width={35} height={35} title="Deploy your backend in one click with Kuberns" />
              </Box>
            </Box>
          </Box>
        </Box>
      </div>
    </Box>
  );
};

export default ServiceTimeLine;
