import { config } from "@/utils/config";


export const executeQuery = async <T>(
  query: any,
  variables?: Record<string, any>
): Promise<T> => {
  try {
    const response = await fetch(`${config.STRAPI_GRAPHQL_URL}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${config.STRAPI_API_TOKEN}`,
      },
      body: JSON.stringify({
        query,
        variables: {
          ...variables,
          slug: variables?.slug || "home",
          locale: "en",
        },
      }),
      next: {
        revalidate: config.NEXT_PUBLIC_REVALIDATE
          ? parseInt(config.NEXT_PUBLIC_REVALIDATE, 10)
          : 0,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `GraphQL request failed (${response.status}): ${errorText}`
      );
    }

    const result = await response.json();

    if (result.errors) {
      console.error("[GraphQL Errors]:", result.errors);
      throw new Error(JSON.stringify(result.errors));
    }

    return result.data;
  } catch (error) {
    console.error("[Query Error]:", error);
    throw error instanceof Error ? error : new Error(String(error));
  }
};
