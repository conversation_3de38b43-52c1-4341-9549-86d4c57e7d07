import { AWS, <PERSON>LENDLY, <PERSON>UP, GITHUB_ICON } from "../vars";

export const HERO_TAG_LINES = [
  { title: "Campus Fund", icon: AWS, position: "prefix", size: 30, alt: "aws" },
  {
    title: "2nd Runner Up",
    icon: CUP,
    position: "suffix",
    size: 20,
    alt: "award",
  },
];

export type HeroTagLineType = (typeof HERO_TAG_LINES)[0];

export const HERO_DEFAULTS = {
  title: "Build, Deploy & Scale smarter saving 40%",
  subtitle:
    "Streamline your deployments, cut costs by 90%, and scale effortlessly with Kuberns - trusted by enterprises, Entrepreneurs & students.",
  taglines: HERO_TAG_LINES,
  action_buttons: [
    {
      title: "Connect & Deploy Now!",
      icon: GITHUB_ICON,
      position: "prefix",
      size: 25,
      alt: "github",
      theme: "light",
      url: "https://dashboard.kuberns.com/login",
    },
    {
      title: "Book a Demo",
      icon: CALENDLY,
      position: "prefix",
      size: 25,
      alt: "calendly",
      theme: "dark",
      url: "/contact",
    },
  ],
  typewriting: {
    prefix_text: "With Kuberns Cloud AI, you can deploy",
    suffix_text: "in one click.",
    text: ["Backend", "Frontend"],
  },
};

export type Hero = typeof HERO_DEFAULTS;

export type HeroTypewriterProps = (typeof HERO_DEFAULTS)["typewriting"];
