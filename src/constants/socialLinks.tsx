import { FaInstagram, FaLinkedin } from "react-icons/fa";
import {
  FaCircleDot,
  FaDiscord,
  FaFacebook,
  FaMedium,
  FaXTwitter,
  FaYoutube,
} from "react-icons/fa6";
import { IoMail } from "react-icons/io5";
import { LuRocket } from "react-icons/lu";

export const SocialLinks = [
  {
    id: 1,
    title: "Twitter",
    url: "https://x.com/Kuberns_cloud?t=cwou8PkW7U9JIHscewk4TQ&s=09",
    icon: <FaXTwitter color="#CACACA" size={20} />,
    isFooter: true,
    doFollow: false,
  },
  {
    id: 2,
    title: "Linkedin",
    url: "https://www.linkedin.com/company/kuberns/",
    icon: <FaLinkedin color="#CACACA" size={20} />,
    isFooter: true,
    doFollow: false,
  },
  {
    id: 3,
    title: "Facebook",
    url: "https://www.facebook.com/profile.php?id=61572017524373",
    icon: <FaFacebook color="#CACACA" size={20} />,
    isFooter: true,
    doFollow: false,
  },
  {
    id: 4,
    title: "Instagram",
    url: "https://www.instagram.com/kuberns.cloud?igsh=Z3VseXFlYzB1dmRp",
    icon: <FaInstagram color="#CACACA" size={20} />,
    isFooter: true,
    doFollow: false,
  },
  {
    id: 5,
    title: "Discord",
    url: "https://discord.gg/KRCM9QneTJ",
    icon: <FaDiscord color="#CACACA" size={20} />,
    isFooter: true,
    doFollow: false,
  },

  {
    id: 6,
    title: "Medium",
    url: "https://medium.com/@kuberns.cloud",
    icon: <FaMedium color="#CACACA" size={20} />,
    isFooter: true,
    doFollow: false,
  },
  {
    id: 7,
    title: "Youtube",
    url: "https://www.youtube.com/@kuberns_cloud",
    icon: <FaYoutube color="#CACACA" size={20} />,
    isFooter: true,
    doFollow: false,
  },
  {
    id: 20,
    title: "<EMAIL>",
    url: "mailto:<EMAIL>",
    icon: <IoMail color="#CACACA" size={20} />,
    isFooter: false,
    doFollow: false,
  },
];

export const EarnedBadges = [
  {
    id: 1,
    title: "Tiny Launch",
    url: "https://www.tinylaun.ch/",
    icon: (
      <img
        src="https://tinylaun.ch/tinylaunch_badge_2.svg"
        alt="TinyLaunch Badge"
        style={{
          width: 202,
          height: "auto",
        }}
      />
    ),
    isFooter: true,
    doFollow: true,
  },
  {
    id: 2,
    title: "Open-Launch Top 1 Daily Winner",
    url: "https://open-launch.com/",
    icon: (
      <img
        src="https://open-launch.com/images/badges/top1-dark.svg"
        alt="Open-Launch Top 1 Daily Winner"
        style={{
          width: 202,
          height: "auto",
        }}
      />
    ),
    isFooter: true,
    doFollow: true,
  },
];
