import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
  const response = NextResponse.next();

  const path = request.nextUrl.pathname;

  // Pages to exclude from indexing
  const noIndexPaths = ["/privacy-policy", "/terms-conditions"];

  if (noIndexPaths.includes(path)) {
    response.headers.set("X-Robots-Tag", "noindex, nofollow");
  } else {
    response.headers.set("X-Robots-Tag", "index, follow");
  }

  return response;
}
