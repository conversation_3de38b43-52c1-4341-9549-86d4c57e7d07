"use client";

import React, { ButtonHTMLAttributes, ReactNode } from "react";
import cn from "@/helpers/cn";
import Image from "next/image";

const THEME = {
  light: {
    backgroundColor: "#FFF",
    color: "#1a202c",
    transition: "box-shadow 0.15s",
    border: "1px solid rgba(255, 255, 255, 0.50",
    boxShadow:
      "0 0 4px 0 rgba(0, 0, 0, 0.25) inset, 0 8px 32.9px 0 rgba(255, 255, 255, 0.25);",
  },
  dark: {
    backgroundColor: "#0D0D0D",
    color: "#fff",
    transition: "background 0.15s",
    border: "1px solid rgba(255, 255, 255, 0.25)",
    boxShadow: "0 0 4px 0 rgba(0, 0, 0, 0.25) inset",
  },
  default: {
    borderRadius: "12px",
    border: "1px solid #161616",
    background: "#1B1B1B",
    backdropFilter: "blur(6.074999809265137px)",
  },
};

type Theme = keyof typeof THEME;

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  icon?: string | ReactNode; // string for image src, ReactNode for node type icon
  title: string;
  position?: "prefix" | "suffix";
  size?: number;
  alt?: string;
  theme?: Theme;
  target?: "_blank" | "_self" | "_parent" | "_top";
  className?: string;
  textClassName?: string;
  url?: string; // for the custom open url behavior on click
}

function Button(props: ButtonProps) {
  const {
    icon, // Expecting a React component
    title,
    position = "prefix",
    size = 20,
    alt,
    theme = "light",
    target = "_blank",
    className,
    textClassName,
    ...rest
  } = props;

  return (
    <button
      {...rest}
      className={cn(
        "flex flex-row items-center justify-center px-3 py-2 gap-2 rounded-xl font-medium text-base transition-all duration-150 ",
        className
      )}
      style={THEME[theme as Theme]}
      onClick={() => {
        if (props.url) {
          window.open(props.url, target);
        }
      }}
    >
      {icon && position === "prefix" && (
        <span className="flex items-center">
          {typeof icon === "string" && (
            <Image src={icon} width={size} height={size} alt={alt || ""} />
          )}
          {typeof icon === "object" && icon}
        </span>
      )}
      <span className={cn("font-semibold text-base ", textClassName)}>
        {title}
      </span>
      {icon && position === "suffix" && (
        <span className="flex items-center">
          {typeof icon === "string" && (
            <Image src={icon} width={size} height={size} alt={alt || ""} />
          )}
          {typeof icon === "object" && icon}
        </span>
      )}
    </button>
  );
}

export default Button;
