.box {
  position: relative;
  display: flex;
  background-color: #161616;
  border-radius: 30px;
  border-radius: 36px;
  background: linear-gradient(
    45deg,
    rgba(253, 250, 241, 0.15) 14.64%,
    rgba(237, 204, 114, 0.15) 30.22%,
    rgba(226, 183, 72, 0.15) 36.34%,
    rgba(212, 164, 58, 0.15) 39.84%,
    rgba(221, 238, 253, 0.15) 61.39%,
    rgba(126, 174, 217, 0.15) 76.86%,
    rgba(73, 134, 190, 0.15) 85.36%
  );
  backdrop-filter: blur(4px);
}

.box::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 30px;
  padding: 1.5px;
  pointer-events: none;
}

.emp_dev_text {
  display: inline-block;
  color: #cbd6e0;
  text-align: center;
  text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  font-family: Manrope;
  font-size: 48px;
  font-style: normal;
  font-weight: 400;
}
