import { COIN, COIN2, COIN3, GOOGLE_LINK } from "@/constants/vars";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import { useEffect, useState } from "react";
import ButtonWithIcon from "../../IconButton";

const ButtonSlider = () => {
  const buttons = [
    {
      id: 1,
      text: "Deploy like a PRO for",
      italicText: "Free",
      icon: COIN,
    },
    {
      id: 2,
      text: "Deploy in minutes for",
      italicText: "Free",
      icon: COIN2,
    },
    {
      id: 3,
      text: "Grab your",
      italicText: "500 Kredits.",
      icon: COIN3,
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % buttons.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [buttons.length]);

  return (
    <div className="relative flex flex-1 justify-end items-center">
      <AnimatePresence mode="wait">
        <motion.div
          key={buttons[currentIndex].id}
          initial={{
            rotateX: -90,
            scaleY: 0,
            opacity: 0,
          }}
          animate={{
            rotateX: 0,
            scaleY: 1,
            opacity: 1,
          }}
          exit={{
            rotateX: 90,
            scaleY: 0,
            opacity: 0,
          }}
          transition={{
            type: "spring",
            mass: 0.2,
            stiffness: 250,
            damping: 25,
          }}
          style={{
            transformStyle: "preserve-3d",
            backfaceVisibility: "hidden",
          }}
          className="w-full flex justify-center items-center md:justify-end"
        >
          <ButtonWithIcon
            url={GOOGLE_LINK}
            text={buttons[currentIndex].text}
            italicText={buttons[currentIndex].italicText}
            icon={
              <Image
                src={buttons[currentIndex].icon}
                width={20}
                height={20}
                alt="coin"
                title="Deploy your backend in one click with Kuberns"
              />
            }
            prefix={true}
          />
        </motion.div>
      </AnimatePresence>
    </div >
  );
};

export default ButtonSlider;
