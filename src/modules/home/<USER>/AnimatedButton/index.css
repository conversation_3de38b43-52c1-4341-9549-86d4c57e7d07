.button-container {
	display: flex;
	justify-content: center;
	align-items: center;
	/* width: 100%; */
	height: 100%;
	overflow: hidden;
	position: relative;
  }
  
  .button-slide {
	display: flex;
	position: absolute;
	transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
	opacity: 0;
	transform: translateX(100%);
  }
  
  .button-slide.visible {
	opacity: 1;
	transform: translateX(0);
  }
  
  .button-wrapper {
	display: inline-block;
  }
  
  .button-slide:nth-child(1) {
	transform: translateX(-100%);
  }
  
  .button-slide:nth-child(2) {
	transform: translateX(0);
  }
  
  .button-slide:nth-child(3) {
	transform: translateX(100%);
  }
  