import { Hero, HERO_DEFAULTS } from "@/constants/defaults/hero";
import DotSeparator from "@/constants/icons/DotSeparator";
import Button from "@/modules/common/Button";
import React from "react";
import HeroTypewriter from "./components/hero-typewriter";
import HeroTagLine from "./components/tag-line";

function HeroHome({ content }: { content: Hero }) {
  const { taglines, title, subtitle, action_buttons, typewriting } = content;

  return (
    <div className="flex flex-col w-full items-center justify-center pt-16 md:pt-32">
      <div className="flex items-center justify-center gap-3">
        {taglines.map((tagline, index) => {
          return (
            <React.Fragment key={`tagline-${index}`}>
              <HeroTagLine {...tagline} />
              {index !== taglines.length - 1 && <DotSeparator />}
            </React.Fragment>
          );
        })}
      </div>
      <div className="w-[300px] md:w-[802px] flex items-center justify-center py-8 flex-col text-center gap-5">
        <h1 className="text-[44px] md:text-[54px] text-tx-white leading-tight">
          {title}
        </h1>
        <h2 className="text-tx-secondary-2 text-lg w-[349px] md:w-[662px]">
          {subtitle}
        </h2>
      </div>
      <div className="flex flex-col md:flex-row items-center justify-center w-full gap-5">
        {action_buttons.map((btn, index) => {
          return <Button key={`btn-${index}`} target="_self" {...btn} />;
        })}
      </div>
      <div className="pt-6 w-full items-center justify-center">
        <HeroTypewriter content={typewriting} />
      </div>
    </div>
  );
}

export default HeroHome;
