"use client";

import TypeWriterComp from "@/components/TypeWriter";
import { HeroTypewriterProps } from "@/constants/defaults/hero";
import { Box } from "@mui/material";
import React from "react";

function HeroTypewriter({ content }: { content: HeroTypewriterProps }) {
  return (
    <div className="w-full flex flex-col md:flex-row gap-2 md:gap-1 font-dmsans text-base md:text-lg justify-center items-center text-center py-3 text-tx-secondary-2">
      <p className="font-light px-1 text-sm md:text-lg ">
        {content.prefix_text}
      </p>
      <div className="flex text-white flex-row gap-0 px-1.5 py-0 rounded-md bg-stone-950 border border-stone-900">
        <TypeWriterComp text={content.text} />
      </div>
      <p className=" font-light text-sm  md:text-lg ">{content.suffix_text}</p>
    </div>
  );
}

export default HeroTypewriter;
