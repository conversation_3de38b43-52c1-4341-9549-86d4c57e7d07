import { useEffect, useState } from "react";

function useMediaQuery(query: string) {
  // Initialize state with current match (during SSR, this will be false).
  const [matches, setMatches] = useState(() => {
    if (typeof window !== "undefined") {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  useEffect(() => {
    if (typeof window === "undefined") {
      return;
    }

    const mediaQueryList = window.matchMedia(query);
    const documentChangeHandler = () => setMatches(mediaQueryList.matches);

    // Listen for changes
    mediaQueryList.addListener(documentChangeHandler);

    // Initial check
    setMatches(mediaQueryList.matches);

    return () => {
      mediaQueryList.removeListener(documentChangeHandler);
    };
  }, [query]);

  return matches;
}

export default useMediaQuery;
