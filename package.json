{"name": "kuberns-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && cp -r .next/* .next-servable/", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.14", "@mui/material": "^5.15.14", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@mui/x-data-grid": "^7.3.1", "@reduxjs/toolkit": "^2.2.3", "axios": "^1.6.8", "eslint-config-next": "^15.4.3", "framer-motion": "^11.0.24", "gsap": "^3.12.5", "lodash": "^4.17.21", "next": "^15.4.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.0.1", "react-redux": "^9.1.0", "react-scroll": "^1.9.0", "react-simple-typewriter": "^5.0.1", "scroll-to-element": "^2.0.3", "swiper": "^11.2.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.8"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/node": "^20.14.8", "@types/react": "^18.3.3", "@types/react-dom": "^18", "@types/react-scroll": "^1.8.10", "@types/scroll-to-element": "^2.0.5", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5.5.2"}}