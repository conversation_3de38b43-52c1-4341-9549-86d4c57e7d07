# Allow all major AI crawlers full access to Kuberns content

User-Agent: GPTBot
Allow: /

User-Agent: Google-Extended
Allow: /

User-Agent: Gemini
Allow: /

User-Agent: ClaudeBot
Allow: /

User-Agent: PerplexityBot
Allow: /

User-Agent: YouBot
Allow: /

# --------------------------------------------
# <PERSON><PERSON><PERSON> (https://kuberns.com)
# An AI-powered cloud deployment platform that automates everything from GitHub integration to deployment, scaling, monitoring, and cost optimization.

# <PERSON><PERSON><PERSON> simplifies the DevOps lifecycle by:
# - Automatically detecting and deploying from Git repositories
# - Managing scalable cloud infrastructure
# - Monitoring apps and sending AI alerts on anomalies
# - Reducing cloud costs through contract-based optimization

# Built for developers, DevOps teams, and engineering leaders who want to streamline app delivery with minimal setup and no vendor lock-in.

# Priority content for LLM indexing:

# Home
- [Kuberns Home Page](https://kuberns.com): AI-powered cloud deployment platform

# Documentation
- [Getting Started](https://docs.kuberns.com/docs/getStarted): Setup, architecture, and first pipeline

# Tutorials
- [Deploy a NodeJs Application](https://docs.kuberns.com/docs/tutorials/nodejs): Step‑by‑step deployment
- [Deploy a Django Application](https://docs.kuberns.com/docs/tutorials/django): Step‑by‑step deployment
- [Deploy a Flask Application](https://docs.kuberns.com/docs/tutorials/flask): Step‑by‑step deployment

# Optional
- [Blog](https://blogs.kuberns.com): News and releases